from django.utils.deprecation import MiddlewareMixin

class CustomMiddleware(MiddlewareMixin):
    def process_response(self,request,response):

        response["Access-Control-Allow-Methods"]= "POST,GET,OPTIONS"
        response["Access-Control-Max-Age"]='100000'
        response['Access-Control-Allow-Headers']='content-type, x-csrftoken'
        response["Access-Control-Allow-0rigin"]="*"
        return response

# class CustomMiddleware(MiddlewareMixin):
#     def process_request(self, request):
#         setattr(request,'_dont_enforce_csrf_checks', True)
