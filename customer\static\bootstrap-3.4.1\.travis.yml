language: node_js
git:
  depth: 3
node_js:
  - "6"
env:
  matrix:
    - TWBS_TEST=core
    - TWBS_TEST=validate-html
    - TWBS_TEST=browserstack
before_install:
  - "export TRAVIS_COMMIT_MSG=\"$(git log --format=%B --no-merges -n 1)\""
  - echo "$TRAVIS_COMMIT_MSG" | grep '\[skip validator\]'; export TWBS_DO_VALIDATOR=$?; true
  - echo "$TRAVIS_COMMIT_MSG" | grep '\[skip browserstack\]'; export TWBS_DO_BROWSERSTACK=$?; true
install:
  - bundle install --deployment --jobs=3 --retry=3 --clean
  - npm install
cache:
  directories:
    - node_modules
    - vendor/bundle
notifications:
  email: false
