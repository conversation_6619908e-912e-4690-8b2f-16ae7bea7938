{% extends 'base.html' %}
{% block content %}
{% load static %}

<style>
    .container1{
        max-width: 1200px;
        margin:  0 auto;
        margin-top: 100px;
    }
    .book-header {
        display: flex;
        margin-bottom: 20px;
        gap: 20px;
    }
    .book-cover {
        width: 200px;
        height: auto;
        border-radius: 5px;
    }
    .book-info {
        flex: 1;
    }
    .book-title {
        font-size: 24px;
        margin: 0 0 10px;
        color: #2c3e50;
    }
    .book-meta {
        color: #7f8c8d;
        margin-bottom: 15px;
    }
    .book-description {
        margin-top: 20px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 5px;
    }
    .back-link {
        display: inline-block;
        margin-top: 20px;
        padding: 8px 16px;
        background: #3498db;
        color: white;
        text-decoration: none;
        border-radius: 4px;
    }

.contents-section {
    margin-top: 30px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 25px;
}

.contents-section h2 {
    color: #343a40;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

/* 章节列表样式 */
.content-list {
    list-style: none;
    padding: 0;
    margin-top: 20px;
}

.content-item {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.content-item:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.content-title {
    font-size: 18px;
    color: #495057;
    font-weight: 500;
    text-decoration: none;
}

.content-title:hover {
    color: #007bff;
}

.delete-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.2s;
    margin-left: 10%;
}

.delete-btn:hover {
    background: #c82333;
}

/* 添加章节表单样式 */
.add-content-form {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.add-content-form h3 {
    margin-top: 0;
    color: #495057;
}

.form-group {
    margin-bottom: 15px;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 16px;
}

.form-control:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
}

.btn-primary {
    background: #007bff;
    color: white;
    border: none;
}

.btn-primary:hover {
    background: #0069d9;
}

/* 空状态提示 */
.empty-message {
    color: #6c757d;
    text-align: center;
    padding: 20px;
    font-style: italic;
}

   
.book-detail-container {
        margin: 20px auto;
        font-family: Arial, sans-serif;
    }
    
    /* 标签按钮样式 */
    .tab-buttons {
        display: flex;
        border-bottom: 1px solid #ddd;
        margin-bottom: 20px;
    }
    
    .tab-button {
        padding: 10px 20px;
        background: none;
        border: none;
        cursor: pointer;
        font-size: 16px;
        font-weight: bold;
        color: #666;
        position: relative;
        transition: all 0.3s;
    }
    
    .tab-button:hover {
        color: #333;
    }
    
    .tab-button.active {
        color: #007bff;
    }
    
    .tab-button.active::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: #007bff;
    }
    

    .tab-content {
        display: none;
        animation: fadeIn 0.5s;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
    
    /* 卡片样式 */
    .card {
        background: #fff;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    /* 表单样式 */
    .form-group {
        margin-bottom: 15px;
    }
    
    label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
    }
    
    input[type="text"],
    textarea {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    }
    
    textarea {
        min-height: 100px;
        resize: vertical;
    }
    
    .submit-btn {
        background-color: #007bff;
        color: white;
        border: none;
        padding: 10px 15px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.3s;
    }
    
    .submit-btn:hover {
        background-color: #0056b3;
    }
    
    /* 章节列表样式 */
    .content-list {
        list-style: none;
        padding: 0;
    }
    
    .content-item {
        margin-bottom: 10px;
    }
    
    .content-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        background: #f9f9f9;
        border-radius: 4px;
    }
    
    .content-title {
        color: #007bff;
        text-decoration: none;
        font-weight: bold;
    }
    
    .content-title:hover {
        text-decoration: underline;
    }
    
    .delete-btn {
        background-color: #dc3545;
        color: white;
        border: none;
        padding: 5px 10px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 13px;
    }
    
    .delete-btn:hover {
        background-color: #bb2d3b;
    }
    
    /* 书评样式 */
    .comment-list {
        max-height: 500px;
        overflow-y: auto;
    }
    
    .comment-card {
        padding: 15px;
        border-radius: 4px;
        margin-bottom: 15px;
    }
    
    .comment-content {
        margin: 0 0 10px 0;
    }
    
    .comment-meta {
        font-size: 12px;
        color: #666;
    }
    
    .empty-message {
        color: #666;
        font-style: italic;
        padding: 20px;
        text-align: center;
    }

</style>
<script src="../static/jquery-3.6.0.min.js"></script>
<div class="container1">
<div class="book-header">
    <img src="{{ book.img_url }}" alt="{{ book.name }}" class="book-cover">
    <div class="book-info">
        <h1 class="book-title">{{ book.name }}</h1>
        <div class="book-meta">
            <p><strong>作者:</strong> {{ book.author }}</p>
            <p><strong>出版社:</strong> {{ book.cbs }}</p>
            <p><strong>出版年份:</strong> {{ book.nf }}</p>
            <p><strong>价格:</strong> ¥{{ book.price }}</p>
            <p><strong>分类:</strong> {{ book.category.name }}</p>
        </div>
    </div>
</div>

<div class="book-description">
    <h3>图书简介</h3>
    <p>{{ book.des|linebreaksbr }}</p>
    
    {% if book.content %}
    <h3>内容简介</h3>
    <p>{{ book.content|linebreaksbr }}</p>
    {% endif %}
</div>
<a href="/ahome/" class="back-link">返回列表</a>


<div class="contents-section">
<div class="book-detail-container">
    <!-- 标签页导航 -->
    <div class="tab-buttons">
        <button class="tab-button active" onclick="openTab(event, 'chapters')">章节列表</button>
        <button class="tab-button" onclick="openTab(event, 'comments')">书评</button>
    </div>

    <!-- 章节列表标签页 -->
    <div id="chapters" class="tab-content" style="display: block;">
        <div class="contents-section">
            <!-- 添加新章节的表单 -->
            <div class="add-content-form card">
                <h3>添加新章节</h3>
                <form method="post" action="{% url 'abook_detail' pk=book.pk %}">
                    {% csrf_token %}
                    <input type="hidden" name="add" value="true">
                    <div class="form-group">
                        <label for="title">标题:</label>
                        <input type="text" id="title" name="title" required>
                    </div>
                    <div class="form-group">
                        <label for="content">内容:</label>
                        <textarea id="content" name="content" rows="5"></textarea>
                    </div>
                    <button type="submit" class="submit-btn">添加章节</button>
                </form>
            </div>
            
            <h2>章节列表</h2>
            
            {% if contents %}
            <ul class="content-list">
                {% for content in contents %}
                <li class="content-item">
                    <div class="content-header">
                        <a href="{% url 'acontent_detail' book_id=book.pk content_id=content.pk %}" 
                           class="content-title">
                            {{ content.title }}
                        </a>
                        <form method="post">
                            {% csrf_token %}
                            <input type="hidden" name="delete" value="true">
                            <input type="hidden" name="content_id" value="{{ content.id }}">
                            <button type="submit" class="delete-btn">删除</button>
                        </form>
                    </div>
                </li>
                {% endfor %}
            </ul>
            {% else %}
            <p class="empty-message">暂无章节内容</p>
            {% endif %}
        </div>
    </div>

    <!-- 书评标签页 -->
    <div id="comments" class="tab-content">
        <div class="comments-section">
            <!-- 添加新书评的表单 -->
            <div class="add-comment-form card">
                <h3>添加书评</h3>
                <form method="post" action="{% url 'abook_detail' pk=book.pk %}">
                    {% csrf_token %}
                    <input type="hidden" name="add_comment" value="true">
                    <div class="form-group">
                        <label for="comment_content">评论内容:</label>
                        <textarea id="comment_content" name="content" rows="3" required></textarea>
                    </div>
                    <button type="submit" class="submit-btn">提交评论</button>
                </form>
            </div>
            
            <h2>书评列表</h2>
            
            {% if book.comments.all %}
            <div class="comment-list">
                {% for comment in book.comments.all %}
                <div class="comment-card">
                    <p class="comment-content">{{ comment.content }}</p>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <p class="empty-message">暂无书评</p>
            {% endif %}
        </div>
    </div>
</div>
</div>

</div>


<script>
    function openTab(evt, tabName) {
        // 隐藏所有标签内容
        var tabContents = document.getElementsByClassName("tab-content");
        for (var i = 0; i < tabContents.length; i++) {
            tabContents[i].style.display = "none";
        }
        
        // 移除所有按钮的active类
        var tabButtons = document.getElementsByClassName("tab-button");
        for (var i = 0; i < tabButtons.length; i++) {
            tabButtons[i].className = tabButtons[i].className.replace(" active", "");
        }
        
        // 显示当前标签内容并激活按钮
        document.getElementById(tabName).style.display = "block";
        evt.currentTarget.className += " active";
    }
</script>

{% endblock %}