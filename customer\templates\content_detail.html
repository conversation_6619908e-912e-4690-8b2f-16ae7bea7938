{% extends 'base.html' %}
{% block content %}
<style>

.container {
    max-width: 1200px;
    margin: 30px auto;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    font-family: 'Arial', sans-serif;
    margin-top: 80px;
}

.container h1 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 20px;
    text-align: center;
    font-weight: bold;
}

/* 书籍链接样式 */
.container p a {
    text-decoration: none;
    color: #007bff;
    font-weight: bold;
}

.container p a:hover {
    color: #0056b3;
}

/* 内容区域样式 */
.content-body {
    margin-top: 20px;
    line-height: 1.6;
    color: #555;
    font-size: 1.1rem;
}

/* 按钮样式 */
.btn-secondary {
    display: inline-block;
    padding: 10px 20px;
    background-color: #6c757d;
    color: white;
    text-align: center;
    border-radius: 5px;
    text-decoration: none;
    margin-top: 20px;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

</style>

<div class="container">
    <h1>{{ content.title }}</h1>
    <p>所属图书: <a href="{% url 'abook_detail' pk=book.pk %}">{{ book.title }}</a></p>
    
    <div class="content-body">
        {{ content.content|linebreaks }}
    </div>
    
    <a href="{% url 'abook_detail' pk=book.pk %}" class="btn btn-secondary">返回图书详情</a>
</div>
{% endblock %}