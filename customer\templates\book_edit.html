
{% extends 'base.html' %}
{% block content %}
{% load static %}

<style>

    .edit-container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-top: 80px;
    }
    h1 {
        color: #2c3e50;
        margin-bottom: 30px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
    }
    .form-group {
        margin-bottom: 20px;
    }
    label {
        display: block;
        margin-bottom: 8px;
        font-weight: bold;
        color: #495057;
    }
    input[type="text"],
    input[type="number"],
    input[type="url"],
    textarea,
    select {
        width: 100%;
        padding: 10px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;
    }
    textarea {
        min-height: 100px;
        resize: vertical;
    }
    .form-actions {
        margin-top: 30px;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }
    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.3s;
    }
    .btn-save {
        background-color: #2ecc71;
        color: white;
    }
    .btn-save:hover {
        background-color: #27ae60;
    }
    .btn-cancel {
        background-color: #95a5a6;
        color: white;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }
    .btn-cancel:hover {
        background-color: #7f8c8d;
    }
    .field-helptext {
        font-size: 12px;
        color: #6c757d;
        margin-top: 5px;
    }
    .required-field::after {
        content: " *";
        color: #e74c3c;
    }
</style>

<div class="edit-container">
    <h1>编辑图书信息</h1>
    
    <form method="post">
        {% csrf_token %}
        
        <div class="form-group">
            <label for="id_bid" class="{% if form.bid.field.required %}required-field{% endif %}">图书编号</label>
            <input type="text" id="id_bid" name="bid" value="{{ form.bid.value|default_if_none:'' }}">
            {% if form.bid.help_text %}
                <p class="field-helptext">{{ form.bid.help_text }}</p>
            {% endif %}
        </div>
        
        <div class="form-group">
            <label for="id_name" class="required-field">书名</label>
            <input type="text" id="id_name" name="name" value="{{ form.name.value }}" required>
        </div>
        
        <div class="form-group">
            <label for="id_author" class="required-field">作者</label>
            <input type="text" id="id_author" name="author" value="{{ form.author.value }}" required>
        </div>
        
        <div class="form-group">
            <label for="id_img_url">封面图片URL</label>
            <input type="url" id="id_img_url" name="img_url" value="{{ form.img_url.value|default_if_none:'' }}">
            {% if book.img_url %}
                <div style="margin-top: 10px;">
                    <img src="{{ book.img_url }}" alt="当前封面" style="max-width: 200px; height: auto;">
                </div>
            {% endif %}
        </div>
        
        <div class="form-group">
            <label for="id_biy">标语</label>
            <input type="text" id="id_biy" name="biy" value="{{ form.biy.value|default_if_none:'' }}">
        </div>
        
        <div class="form-group">
            <label for="id_price" class="required-field">价格</label>
            <input type="number" id="id_price" name="price" value="{{ form.price.value }}" step="0.01" min="0" required>
        </div>
        
        <div class="form-group">
            <label for="id_nf" class="required-field">出版年份</label>
            <input type="text" id="id_nf" name="nf" value="{{ form.nf.value }}" required>
        </div>
        
        <div class="form-group">
            <label for="id_cbs" class="required-field">出版社</label>
            <input type="text" id="id_cbs" name="cbs" value="{{ form.cbs.value }}" required>
        </div>
        
        <div class="form-group">
            <label for="id_kcl" class="required-field">库存量</label>
            <input type="number" id="id_kcl" name="kcl" value="{{ form.kcl.value }}" min="0" required>
        </div>
        
        <div class="form-group">
            <label for="id_category" class="required-field">分类</label>
            <select id="id_category" name="category" required>
                {% for category in categories %}
                    <option value="{{ category.id }}" {% if category.id == form.category.value %}selected{% endif %}>
                        {{ category.name }}
                    </option>
                {% endfor %}
            </select>
        </div>
        
        <div class="form-group">
            <label for="id_des">简介</label>
            <textarea id="id_des" name="des">{{ form.des.value|default_if_none:'' }}</textarea>
        </div>
        
        <div class="form-group">
            <label for="id_content">内容</label>
            <textarea id="id_content" name="content">{{ form.content.value|default_if_none:'' }}</textarea>
        </div>
        
        <div class="form-actions">
            <a href="/ahome/" class="btn btn-cancel">取消</a>
            <button type="submit" class="btn btn-save">保存更改</button>
        </div>
    </form>
</div>

{% endblock %}