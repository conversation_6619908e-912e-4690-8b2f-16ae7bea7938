---
layout: default
title: Wall of browser bugs
slug: browser-bugs
lead: "A list of the browser bugs that <PERSON><PERSON><PERSON> is currently grappling with."
fullwidth: true
---


<div class="bs-docs-section">
  <h1 id="browser-bugs" class="page-header">Browser bugs</h1>

  <p class="lead">Bootstrap currently works around several outstanding browser bugs in major browsers to deliver the best cross-browser experience possible. Some bugs, like those listed below, cannot be solved by us.</p>
  <p>We publicly list browser bugs that are impacting us here, in the hopes of expediting the process of fixing them. For information on Bootstrap's browser compatibility, <a href="{{ site.baseurl }}/getting-started/#support">see our browser compatibility docs</a>.</p>
  <p>See also:</p>
  <ul>
    <li><a href="https://bugs.chromium.org/p/chromium/issues/detail?id=536263">Chromium issue 536263: [meta] Issues affecting Bootstrap</a></li>
    <li><a href="https://bugzilla.mozilla.org/show_bug.cgi?id=1230801">Mozilla bug 1230801: Fix the issues that affect Bootstrap</a></li>
    <li><a href="https://bugs.webkit.org/show_bug.cgi?id=159753">WebKit bug 159753: [meta] Issues affecting Bootstrap</a></li>
    <li><a href="https://docs.google.com/document/d/1LPaPA30bLUB_publLIMF0RlhdnPx_ePXm7oW02iiT6o">jQuery's browser bug workarounds</a></li>
  </ul>

  <div class="table-responsive">
    <table class="bs-docs-browser-bugs table table-bordered table-hover">
      <thead>
        <tr>
          <th>Browser(s)</th>
          <th>Summary of bug</th>
          <th>Upstream bug(s)</th>
          <th>Bootstrap issue(s)</th>
        </tr>
      </thead>
      <tbody>
        {% for bug in site.data.browser-bugs %}
        <tr>
          <td>{{ bug.browser }}</td>
          <td>{{ bug.summary | markdownify | bugify }}</td>
          <td>{{ bug.upstream_bug | bugify }}</td>
          <td>{{ bug.origin | bugify }}</td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
</div>

<div class="bs-docs-section">
  <h1 id="most-wanted" class="page-header">Most wanted features</h1>

  <p class="lead">There are several features specified in Web standards which would allow us to make Bootstrap more robust, elegant, or performant, but aren't yet implemented in certain browsers, thus preventing us from taking advantage of them.</p>
  <p>We publicly list these "most wanted" feature requests here, in the hopes of expediting the process of getting them implemented.</p>

   <div class="table-responsive">
     <table class="bs-docs-browser-bugs table table-bordered table-hover">
       <thead>
         <tr>
           <th>Browser(s)</th>
           <th>Summary of feature</th>
           <th>Upstream issue(s)</th>
           <th>Bootstrap issue(s)</th>
         </tr>
       </thead>
       <tbody>
         {% for feat in site.data.browser-features %}
         <tr>
           <td>{{ feat.browser }}</td>
           <td>{{ feat.summary | markdownify | bugify }}</td>
           <td>{{ feat.upstream_bug | bugify }}</td>
           <td>{{ feat.origin | bugify }}</td>
         </tr>
         {% endfor %}
       </tbody>
     </table>
   </div>
</div>
