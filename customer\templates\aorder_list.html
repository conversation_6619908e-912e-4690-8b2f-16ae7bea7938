{% extends 'base.html' %}
{% block content %}
{% load static %}

<style>
   .container1 {
    max-width: 1200px;
    margin: 0 auto;
    background: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 0 25px rgba(0, 0, 0, 0.08);
    margin-top: 80px;
}


form {
    margin-bottom: 30px;
    display: flex;
    gap: 10px;
}

input[type="text"] {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 16px;
    transition: all 0.3s;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

input[type="text"]:focus {
    outline: none;
    border-color: #4a90e2;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
}

button[type="submit"] {
    background-color: #4a90e2;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s;
}

button[type="submit"]:hover {
    background-color: #357ab8;
    transform: translateY(-1px);
}

/* 表格样式 */
table {
    width: 100%;
    border-collapse: collapse;
    font-size: 15px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.03);
}

thead tr {
    background-color: #4a90e2;
    color: white;
}

th {
    padding: 15px 20px;
    text-align: left;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 14px;
    letter-spacing: 0.5px;
}

td {
    padding: 14px 20px;
    border-bottom: 1px solid #eee;
}

tbody tr {
    transition: all 0.2s;
}

tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}

tbody tr:hover {
    background-color: #f1f7ff;
}

/* 操作链接样式 */
td a {
    text-decoration: none;
    padding: 5px 8px;
    border-radius: 4px;
    transition: all 0.2s;
    margin: 0 2px;
}

td a:first-child {
    color: #4a90e2;
}

td a:first-child:hover {
    background-color: #e6f0ff;
    text-decoration: underline;
}

td a[style*="color:red"] {
    color: #e74c3c !important;
}

td a[style*="color:red"]:hover {
    background-color: #fde8e6;
    text-decoration: underline;
}

/* 状态标签样式（可根据实际状态添加不同颜色） */
td:nth-child(3) {
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container1 {
        padding: 15px;
    }
    
    form {
        flex-direction: column;
    }
    
    th, td {
        padding: 10px 12px;
        font-size: 14px;
    }
    
    /* 在小屏幕上隐藏某些列 */
    th:nth-child(2), td:nth-child(2) {
        display: none;
    }
}
</style>

<div class="container1">



    <form method="GET">
        <input type="text" name="search" value="{{ search_query }}" placeholder="搜索订单号">
        <button type="submit">搜索</button>
    </form>

    <!-- 订单表格 -->
    <table>
        <thead>
            <tr>
                <th>订单号</th>
                <th>用户</th>
                <th>订单状态</th>
                <th>总价</th>
                <th>创建时间</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            {% for order in orders %}
            <tr>
                <td>{{ order.order_num }}</td>
                <td>{{ order.user.username }}</td>
                <td>{{ order.get_status_display }}</td>
                <td>{{ order.total }}</td>
                <td>{{ order.create_time }}</td>
                <td>
                    <a href="{% url 'aorder_detail' order.id %}">查看详情</a> |
                    <a href="{% url 'adelete_order' order.id %}" style="color:red;">删除</a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>


{% endblock %}