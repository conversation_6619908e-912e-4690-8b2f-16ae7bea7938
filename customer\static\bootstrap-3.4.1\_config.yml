# Dependencies
markdown:         kramdown
highlighter:      rouge

# Permalinks
permalink:        pretty

# Server
source:           docs
destination:      _gh_pages
host:             0.0.0.0
port:             9001
url:              https://getbootstrap.com
baseurl:          /docs/3.4
encoding:         UTF-8
exclude:
  - assets/less/

plugins:
  - jekyll-redirect-from
  - jekyll-sitemap

# Custom vars
current_version:  3.4.1
docs_version:     3.4
repo:             https://github.com/twbs/bootstrap
sass_repo:        https://github.com/twbs/bootstrap-sass

download:
  source:         https://github.com/twbs/bootstrap/archive/v3.4.1.zip
  dist:           https://github.com/twbs/bootstrap/releases/download/v3.4.1/bootstrap-3.4.1-dist.zip
  sass:           https://github.com/twbs/bootstrap-sass/archive/v3.4.1.tar.gz

blog:             https://blog.getbootstrap.com
expo:             https://expo.getbootstrap.com
themes:           https://themes.getbootstrap.com

cdn:
  # See https://www.srihash.org for info on how to generate the hashes
  css:            https://stackpath.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css
  css_hash:       "sha384-HSMxcRTRxnN+Bdg0JdbxYKrThecOKuH5zCYotlSAcp1+c8xmyTe9GYg1l9a69psu"
  css_theme:      https://stackpath.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap-theme.min.css
  css_theme_hash: "sha384-6pzBo3FDv/PJ8r2KRkGHifhEocL+1X2rVCTTkUfGk7/0pbek5mMa1upzvWbrUbOZ"
  js:             https://stackpath.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js
  js_hash:        "sha384-aJ21OjlMXNL5UyIl/XNwTMqvzeRMZH2w8c5cRVpzpU8Y5bApTppSuUkhZXN0VxHd"
