{"version": 3, "sources": ["docs.css", "docs/assets/less/syntax.less", "docs/assets/css/docs.css", "docs/assets/less/ie10-viewport-bug-workaround.less", "docs/assets/less/buttons.less", "docs/assets/less/booticon.less", "docs/assets/less/skip-link.less", "docs/assets/less/nav.less", "docs/assets/less/footer.less", "docs/assets/less/masthead.less", "docs/assets/less/page-header.less", "docs/assets/less/ads.less", "docs/assets/less/featurettes.less", "docs/assets/less/featured-sites.less", "docs/assets/less/demos.less", "docs/assets/less/sidebar.less", "docs/assets/less/examples.less", "docs/assets/less/callouts.less", "docs/assets/less/swatches.less", "docs/assets/less/team.less", "docs/assets/less/responsive-tests.less", "docs/assets/less/glyphicons.less", "docs/assets/less/customizer.less", "docs/assets/less/brand.less", "docs/assets/less/clipboard-js.less", "docs/assets/less/anchor.less", "docs/assets/less/algolia.less", "docs/assets/less/misc.less"], "names": [], "mappings": "AAAA;;;;;ACEA,KAAO,iBAAA,KACP,GAAK,MAAA,KACL,KAAO,MAAA,KAAa,iBAAA,KACpB,GAAK,MAAA,KACL,GAAK,MAAA,KACL,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,iBAAA,KAAwB,OAAA,IAAA,MAAA,KAC9B,IAAM,WAAA,OACN,IAAM,MAAA,IACN,IAAM,MAAA,KACN,IAAM,iBAAA,KAAwB,OAAA,IAAA,MAAA,KAC9B,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,GAAK,MAAA,KACL,GAAK,MAAA,QACL,IAAM,MAAA,QACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,QACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,GAAK,MAAA,KACL,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,WAAA,OAAoB,MAAA,KAC1B,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KACN,IAAM,MAAA,KCmIN,aDjIA,QCgIA,YD9HiB,MAAA,KAOjB,WACE,QAAA,IAAA,KACA,cAAA,KACA,iBAAA,QACA,OAAA,IAAA,MAAA,QACA,cAAA,IAEF,eACE,QAAA,EACA,WAAA,EACA,cAAA,EACA,WAAA,OACA,YAAA,OACA,iBAAA,YACA,OAAA,EAEF,oBACE,UAAA,QACA,MAAA,KAEF,gCACE,QAAA,aACA,cAAA,KAGF,sBACE,MAAA,KACA,QAAA,KACA,oBAAA,KAAA,iBAAA,KAAA,gBAAA,KAAA,YAAA,KD4HF;;;;AGrNA,cAAoB,MAAA,aAEpB,UAAoB,MAAA,aCZpB,aACE,MAAA,QACA,iBAAA,YACA,aAAA,QFiPF,oBADA,mBE9OE,mBAGE,MAAA,KACA,iBAAA,QACA,aAAA,QAKJ,qBACE,MAAA,KACA,iBAAA,YACA,aAAA,QF8OF,4BADA,2BE3OE,2BAGE,MAAA,QACA,YAAA,KACA,iBAAA,KACA,aAAA,KC3BJ,kBACE,QAAA,MACA,YAAA,IACA,MAAA,KACA,WAAA,OACA,OAAA,QACA,iBAAA,QACA,cAAA,IAGF,qBACE,MAAA,KACA,OAAA,KACA,UAAA,KACA,YAAA,KAGF,qBACE,MAAA,MACA,OAAA,MACA,UAAA,MACA,YAAA,MAGF,0BACE,MAAA,QACA,iBAAA,KAGF,0BACE,iBAAA,YACA,OAAA,IAAA,MAAA,QC7BF,QACE,QAAA,MACA,QAAA,IACA,MAAA,KACA,iBAAA,QACA,QAAA,EAGF,uBACE,QAAA,KACA,QAAA,IAAA,OAGF,eACE,QAAA,EChBF,aACE,cAAA,EACA,iBAAA,KACA,cAAA,EAHF,uBAMI,QAAA,KANJ,2BL2TA,8BKhTI,YAAA,IACA,MAAA,QAZJ,8BAiBM,cAAA,KACA,aAAA,KLkTN,mCACA,yCKrUA,oCAwBM,MAAA,QACA,iBAAA,QAzBN,sCA8BI,iBAAA,QA9BJ,2CAmCM,aAAA,KL6SN,iDK3SM,iDAEE,iBAAA,QACA,aAAA,QAMJ,+CAAA,2BACE,QAAA,MC/CN,gBACE,YAAA,KACA,eAAA,KACA,WAAA,MACA,MAAA,QACA,WAAA,OACA,iBAAA,QAEF,kBACE,MAAA,KAEF,sBACE,aAAA,EACA,cAAA,KAEF,yBACE,QAAA,aAEF,4BACE,YAAA,KAGF,yBACE,gBACE,WAAA,KAEF,kBACE,cAAA,GN4VJ,gBOrXA,kBAEE,SAAA,SACA,QAAA,KAAA,EACA,MAAA,QACA,WAAA,OACA,YAAA,EAAA,IAAA,EAAA,eACA,iBAAA,QACA,iBAAA,wEACA,iBAAA,oDACA,iBAAA,+CACA,iBAAA,kDACA,OAAA,2GACA,kBAAA,SAIF,oCACE,OAAA,EAAA,KAAA,KAEF,qBACE,YAAA,IACA,YAAA,EACA,MAAA,KAEF,wBACE,OAAA,EAAA,KAAA,KACA,UAAA,KACA,MAAA,KAEF,2BACE,WAAA,MACA,cAAA,KACA,MAAA,QAEF,uBACE,MAAA,KACA,QAAA,KAAA,KACA,UAAA,KAGF,yBACE,uBACE,MAAA,MAIJ,yBACE,kBACE,QAAA,KAAA,EAEF,qBACE,UAAA,KAEF,wBACE,UAAA,MAIJ,yBACE,wBACE,MAAA,IACA,UAAA,MChEJ,gBACE,cAAA,KACA,UAAA,KAEF,mBACE,WAAA,EACA,MAAA,KAEF,kBACE,cAAA,EACA,YAAA,IACA,YAAA,IAEF,2BACE,SAAA,SAGF,yBACE,gBACE,YAAA,KACA,eAAA,KACA,UAAA,KACA,WAAA,KAEF,mBACE,UAAA,KACA,YAAA,GAIJ,yBACE,mBRibA,kBQ/aE,aAAA,OC/BJ,WACE,QAAA,MACA,QAAA,KAAA,KAAA,KAAA,MACA,OAAA,KAAA,MAAA,MACA,SAAA,OACA,UAAA,KACA,YAAA,IACA,WAAA,KACA,OAAA,MAAA,QACA,aAAA,IAAA,EAAA,EATF,aAYI,MAAA,KACA,gBAAA,KAGF,yBAAA,WACE,UAAA,MACA,OAAA,KAAA,KAAA,EACA,aAAA,IACA,cAAA,KAGF,yBAAA,WACE,SAAA,SACA,IAAA,EACA,MAAA,KACA,WAAA,EAEA,6BACE,SAAA,QAKN,YACE,MAAA,KACA,YAAA,OAGF,kBACE,QAAA,MACA,MAAA,kBC5CF,oBACE,YAAA,KACA,eAAA,KACA,UAAA,KACA,YAAA,IACA,MAAA,KACA,WAAA,OACA,iBAAA,KACA,cAAA,IAAA,MAAA,QAEF,oCACE,WAAA,EACA,WAAA,EAGF,0BACE,cAAA,IACA,UAAA,KACA,YAAA,IACA,MAAA,KAEF,WACE,MAAA,MACA,OAAA,KAAA,KAEF,uBACE,cAAA,IACA,YAAA,IACA,MAAA,KAEF,wBACE,QAAA,MACA,cAAA,KACA,MAAA,KAEF,8BACE,MAAA,QACA,gBAAA,KAEF,4BACE,QAAA,MACA,cAAA,KAGF,yBACE,oCACE,WAAA,MAGJ,yBACE,oBACE,YAAA,MACA,eAAA,MAEF,0BACE,UAAA,KAEF,0BACE,UAAA,IACA,aAAA,KACA,YAAA,KAEF,oCACE,WAAA,GC/DJ,wBACE,aAAA,KACA,YAAA,KAEF,kCACE,QAAA,IAEF,wCACE,WAAA,EAGF,yBACE,kDACE,uBAAA,IACA,0BAAA,IAEF,iDACE,wBAAA,IACA,2BAAA,KClBJ,wBAEI,cAAA,KAFJ,gBAKO,cAAA,IALP,eAOM,cAAA,KAEJ,yBAAA,aACE,aAAA,MACA,YAAA,MAFF,2BAKI,cAAA,KACA,aAAA,MCZN,uBACE,SAAA,OAEF,yBACE,iBACE,aAAA,MAIJ,gBACE,cAAA,KACA,YAAA,KAIF,iBACE,WAAA,KACA,cAAA,KAIF,2BACE,QAAA,MACA,QAAA,IAAA,KACA,UAAA,KACA,YAAA,IACA,MAAA,QbgmBF,iCa9lBA,iCAEE,aAAA,KACA,MAAA,QACA,gBAAA,KACA,iBAAA,YACA,YAAA,IAAA,MAAA,QbimBF,sCADA,sCa9lBA,gCAGE,aAAA,KACA,YAAA,IACA,MAAA,QACA,iBAAA,YACA,YAAA,IAAA,MAAA,QAIF,2BACE,QAAA,KACA,eAAA,KAEF,gCACE,YAAA,IACA,eAAA,IACA,aAAA,KACA,UAAA,KACA,YAAA,Ib8lBF,sCa5lBA,sCAEE,aAAA,Kb+lBF,2CADA,2Ca5lBA,qCAGE,aAAA,KACA,YAAA,IAIF,ab0lBA,sBaxlBE,QAAA,KACA,QAAA,IAAA,KACA,WAAA,KACA,YAAA,KACA,UAAA,KACA,YAAA,IACA,MAAA,KAEF,mBb0lBA,4BaxlBE,MAAA,QACA,gBAAA,KAEF,sBACE,WAAA,EAGF,yBACE,abylBA,sBavlBE,QAAA,OAKJ,yBACE,iCACE,QAAA,MAGF,uBbslBA,8BaplBE,MAAA,MAEF,uBACE,SAAA,MACA,IAAA,KAEF,8BACE,SAAA,SbwlBF,wCatlBA,+CAEE,WAAA,EACA,cAAA,GAGJ,0BbulBE,uBarlBA,8BAEE,MAAA,OCvHJ,WACE,cAAA,KAEF,yBACE,YAAA,KACA,eAAA,KACA,iBAAA,KACA,iBAAA,oBACA,OAAA,IAAA,MAAA,KACA,OAAA,IAAA,MAAA,mBAQF,YACE,SAAA,SACA,QAAA,KAAA,KAAA,KACA,OAAA,EAAA,MAAA,KACA,aAAA,QAAA,KAAA,KACA,aAAA,MACA,aAAA,IAAA,EACA,mBAAA,MAAA,EAAA,IAAA,IAAA,gBAAA,WAAA,MAAA,EAAA,IAAA,IAAA,gBAGF,kBACE,SAAA,SACA,IAAA,KACA,KAAA,KACA,UAAA,KACA,YAAA,IACA,MAAA,QACA,eAAA,UACA,eAAA,IACA,QAAA,UAGF,0BACE,eAAA,Kd0sBF,qCctsBA,uBAEE,OAAA,MAAA,MAAA,KACA,aAAA,EAAA,EAAA,IACA,cAAA,EAIF,yBACE,YACE,aAAA,EACA,YAAA,EACA,iBAAA,KACA,aAAA,KACA,aAAA,IACA,cAAA,IAAA,IAAA,EAAA,EACA,mBAAA,KAAA,WAAA,KdusBF,qCcrsBA,uBAEE,WAAA,MACA,aAAA,EACA,YAAA,EACA,aAAA,IACA,2BAAA,IACA,0BAAA,IAEF,yCACE,IAAA,MACA,wBAAA,EAEF,uBACE,cAAA,KAKJ,uBACE,MAAA,Kd4sBF,8BAJA,qCAGA,kCAGA,mCAJA,+BAGA,8BAGA,iCACA,gDARA,8BAMA,6BARA,kCADA,0BclsBA,yBdisBA,0BcnrBE,cAAA,EAEF,qBACE,MAAA,KAIF,mCACE,MAAA,QACA,eAAA,OAEF,2BACE,QAAA,KAAA,EACA,aAAA,KAEF,0CACE,WAAA,EAEF,oBd+rBA,oBACA,oBACA,oBACA,oBACA,oBc7rBE,OAAA,EAIF,yBACE,QAAA,KAIF,wBd2rBA,yBACA,2BczrBE,OAAA,IAIF,qCACE,iBAAA,KAIF,iBdurBA,uBcrrBE,WAAA,IACA,cAAA,IAEF,sCACE,WAAA,KdyrBF,6DcrrBA,kCAGI,WAAA,KAGJ,8BACE,cAAA,KAEF,kCACE,OAAA,SAIF,wBACE,UAAA,MAIF,+BACE,cAAA,EdirBF,0Bc/qBA,uBAEE,QAAA,EACA,QAAA,EACA,SAAA,OdirBF,yCc/qBA,sCAEE,YAAA,EdirBF,+Cc/qBA,yCAEE,SAAA,SACA,aAAA,EACA,YAAA,EAEF,uBACE,eAAA,KAEF,6BACE,IAAA,KACA,OAAA,KAEF,yCACE,IAAA,KAEF,0BACE,YAAA,KAEF,+CACE,OAAA,KAEF,kCACE,cAAA,EAEF,yBdgrBE,+Cc/qBA,yCAEE,SAAA,UAKJ,wBACE,WAAA,KACA,cAAA,KAIF,mBACE,WAAA,EAIF,kBACE,iBAAA,QAEF,yBACE,SAAA,SACA,IAAA,KACA,MAAA,KACA,OAAA,KACA,KAAA,KACA,QAAA,EACA,QAAA,MAEF,gCACE,KAAA,KACA,aAAA,KACA,YAAA,KAIF,uCACE,MAAA,KAEF,qCACE,SAAA,OACA,QAAA,MACA,cAAA,IACA,MAAA,KAIF,2BACE,cAAA,KAIF,qBACE,WAAA,OAEF,0BACE,WAAA,IACA,cAAA,IAEF,6BACE,SAAA,SACA,QAAA,aACA,OAAA,KAAA,KACA,QAAA,EAIF,oBACE,eAAA,KACA,iBAAA,QAEF,6BACE,SAAA,SACA,QAAA,MACA,MAAA,KACA,MAAA,MACA,OAAA,KAIF,mBACE,SAAA,SACA,OAAA,MACA,WAAA,KACA,SAAA,KAGF,uCACE,UAAA,MAIF,uBACE,cAAA,EAIF,cACE,aAAA,KACA,aAAA,oBACA,QAAA,EACA,QAAA,KAAA,SACA,mBAAA,EAAA,EAAA,IAAA,oBAAA,WAAA,EAAA,EAAA,IAAA,oBC9TF,YACE,QAAA,KACA,OAAA,KAAA,EACA,OAAA,IAAA,MAAA,KACA,kBAAA,IACA,cAAA,IALF,eAQI,WAAA,EACA,cAAA,IATJ,yBAaI,cAAA,EAbJ,iBAiBI,cAAA,IAjBJ,wBAqBI,WAAA,KAIJ,mBACE,kBAAA,QADF,sBAII,MAAA,QAIJ,oBACE,kBAAA,QADF,uBAII,MAAA,QAIJ,iBACE,kBAAA,QADF,oBAII,MAAA,QClDJ,gBACE,OAAA,EAAA,KACA,SAAA,OAEF,cACE,MAAA,KACA,MAAA,KACA,OAAA,KACA,OAAA,EAAA,IACA,cAAA,IAGF,yBACE,cACE,MAAA,MACA,OAAA,OAKJ,6BACE,iBAAA,KAEF,2BACE,iBAAA,KAEF,sBACE,iBAAA,KAEF,4BACE,iBAAA,KAEF,8BACE,iBAAA,KAEF,+BACE,iBAAA,QAEF,+BACE,iBAAA,QAEF,+BACE,iBAAA,QAEF,8BACE,iBAAA,QAEF,4BACE,iBAAA,QAIF,2BACE,iBAAA,QAEF,iCACE,iBAAA,QAEF,mCACE,iBAAA,QAEF,yBACE,iBAAA,QC9DF,sBACE,YAAA,KACA,MAAA,KAEF,4BACE,MAAA,KACA,gBAAA,KAEF,qBACE,MAAA,MACA,MAAA,MACA,OAAA,KACA,WAAA,IACA,OAAA,KAEF,aACE,MAAA,KACA,MAAA,KACA,aAAA,KACA,cAAA,IChBF,iCACE,YAAA,OAIF,mBlBklCA,+BkBhlCE,QAAA,MACA,YAAA,IACA,MAAA,KAEF,+BACE,YAAA,IAEF,yBACE,WAAA,OAEF,oCACE,MAAA,QACA,iBAAA,kBAEF,mCACE,MAAA,KACA,iBAAA,kBAIF,2BACE,WAAA,IAEF,qCACE,cAAA,KAEF,gCACE,QAAA,MACA,QAAA,KAAA,KACA,UAAA,KACA,YAAA,IACA,YAAA,IACA,WAAA,OACA,cAAA,IlBwlCF,gCADA,gCADA,gCADA,gCADA,iCADA,iCADA,iCkBhlCA,iCAQE,MAAA,KACA,OAAA,IAAA,MAAA,KlBwlCF,uCADA,uCADA,uCADA,uCADA,wCADA,wCADA,wCkBhlCA,wCAQE,MAAA,QACA,iBAAA,QACA,OAAA,IAAA,MAAA,QClEF,eACE,OAAA,EAAA,MAAA,KACA,SAAA,OAEF,oBACE,aAAA,EACA,WAAA,KAEF,kBACE,MAAA,KACA,MAAA,IACA,OAAA,MACA,QAAA,KACA,UAAA,KACA,YAAA,IACA,WAAA,OACA,iBAAA,QACA,OAAA,IAAA,MAAA,KAEF,0BACE,WAAA,IACA,cAAA,KACA,UAAA,KAEF,gCACE,QAAA,MACA,WAAA,OACA,UAAA,WAEF,wBACE,MAAA,KACA,iBAAA,QAGF,yBACE,eACE,aAAA,EACA,YAAA,EAEF,kBACE,MAAA,MACA,UAAA,MCvCJ,uBACE,MAAA,MACA,WAAA,KAIF,qBACE,WAAA,KACA,YAAA,IACA,MAAA,KAEF,kBACE,YAAA,KACA,WAAA,EACA,cAAA,IAEF,kBACE,cAAA,EAEF,kBACE,WAAA,KACA,cAAA,EAEF,8BACE,WAAA,EACA,cAAA,IAEF,gCACE,YAAA,KAAA,CAAA,MAAA,CAAA,QAAA,CAAA,aAAA,CAAA,UACA,iBAAA,QAEF,2BACE,cAAA,IACA,UAAA,KAIF,oBACE,YAAA,IAIF,oCACE,QAAA,KAIF,qBACE,SAAA,MACA,IAAA,EACA,MAAA,EACA,KAAA,EACA,QAAA,KACA,QAAA,KAAA,EACA,MAAA,KACA,iBAAA,QACA,cAAA,IAAA,MAAA,QACA,mBAAA,MAAA,EAAA,IAAA,EAAA,sBAAA,WAAA,MAAA,EAAA,IAAA,EAAA,sBAEF,4BACE,WAAA,KACA,UAAA,KAEF,uBACE,cAAA,EAEF,gCACE,aAAA,IAEF,yBACE,OAAA,KAAA,EAAA,EACA,MAAA,KACA,iBAAA,QACA,aAAA,QACA,mBAAA,MAAA,EAAA,IAAA,IAAA,eAAA,CAAA,EAAA,IAAA,EAAA,qBAAA,WAAA,MAAA,EAAA,IAAA,IAAA,eAAA,CAAA,EAAA,IAAA,EAAA,qBAGF,aACE,SAAA,SACA,QAAA,KACA,cAAA,KACA,MAAA,KACA,WAAA,OACA,OAAA,IAAA,OAAA,KACA,cAAA,IAEF,4BACE,cAAA,IAEF,oCACE,UAAA,KAEF,gBACE,MAAA,MAEF,mBACE,cAAA,KACA,YAAA,IACA,MAAA,KAEF,uBACE,OAAA,QAEF,0BACE,cAAA,ECzGF,gBACE,QAAA,MACA,MAAA,KACA,cAAA,KACA,SAAA,OACA,MAAA,QACA,iBAAA,QACA,cAAA,IAIF,eACE,QAAA,KAAA,EACA,WAAA,OAEF,8BACE,WAAA,IAAA,MAAA,KAEF,yBACE,MAAA,KACA,iBAAA,QAIF,kBrB6xCA,kBqB3xCE,WAAA,EACA,cAAA,EAEF,iCACE,aAAA,KACA,YAAA,KAIF,0BACE,MAAA,KACA,OAAA,KACA,OAAA,KAAA,KAAA,MACA,YAAA,KACA,MAAA,KACA,cAAA,IAEF,6BACE,iBAAA,QAEF,iCACE,iBAAA,QAGF,yBACE,eACE,QAAA,WACA,MAAA,GAEF,8BACE,WAAA,EACA,YAAA,IAAA,MAAA,KAEF,kBACE,UAAA,MCzDJ,cACE,SAAA,SACA,QAAA,KACA,MAAA,MAHF,yBAMI,WAAA,EAIJ,eACE,SAAA,SACA,IAAA,EACA,MAAA,EACA,QAAA,GACA,QAAA,MACA,QAAA,IAAA,IACA,UAAA,KACA,MAAA,QACA,OAAA,QACA,iBAAA,YACA,OAAA,EACA,wBAAA,IACA,0BAAA,IAEA,qBACE,MAAA,KACA,iBAAA,QAIJ,yBACE,cACE,QAAA,OCrCJ,eACE,MAAA,QAGF,yBACE,eACE,QAAA,MAIJ,sBACE,QAAA,IACA,mBAAA,MAAA,KAAA,OAAA,cAAA,MAAA,KAAA,OAAA,WAAA,MAAA,KAAA,OvB43CF,qBuBz3CA,4BAEE,gBAAA,KACA,QAAA,ECbF,sBACE,QAAA,gBADF,wCAKI,MAAA,KACA,UAAA,YACA,UAAA,eACA,QAAA,KAAA,YACA,iBAAA,KACA,gBAAA,YACA,OAAA,IAAA,MAAA,KACA,OAAA,IAAA,MAAA,eACA,mBAAA,EAAA,IAAA,KAAA,iBAAA,WAAA,EAAA,IAAA,KAAA,iBAEA,yBAAA,wCACE,MAAA,MAIF,+CACE,QAAA,eArBN,6DAyBM,QAAA,YACA,SAAA,kBACA,iBAAA,sBACA,OAAA,YA5BN,wDAgCM,WAAA,YAhCN,kDAoCM,mBAAA,KAAA,WAAA,KApCN,oDAyCI,QAAA,YACA,SAAA,kBA1CJ,qEA8CI,QAAA,IAAA,eACA,WAAA,YACA,UAAA,eACA,YAAA,cACA,MAAA,kBACA,cAAA,YAnDJ,6DAuDI,MAAA,eACA,YAAA,YAxDJ,wEA6DI,MAAA,eACA,MAAA,eACA,QAAA,YACA,WAAA,eAhEJ,6DAoEI,MAAA,eACA,MAAA,eACA,QAAA,YAGA,oEACE,QAAA,eAKF,sGAEI,YAAA,eACA,WAAA,eACA,WAAA,IAAA,MAAA,KAnFR,uFAwFM,QAAA,eAxFN,2DA6FI,QAAA,MACA,QAAA,IAAA,eACA,cAAA,YACA,UAAA,eACA,YAAA,cAjGJ,0DAqGI,QAAA,EAAA,KAAA,cACA,WAAA,KACA,UAAA,eACA,YAAA,IACA,YAAA,eAzGJ,gDA6GI,MAAA,eACA,MAAA,eACA,OAAA,eACA,QAAA,KAAA,KAAA,EACA,UAAA,eACA,YAAA,YACA,MAAA,kBACA,WAAA,IAAA,MAAA,KApHJ,sDAwHI,QAAA,iBACA,SAAA,kBACA,MAAA,kBACA,YAAA,YACA,WAAA,cA5HJ,+DAgII,MAAA,QACA,iBAAA,KAjIJ,mGAqII,mBAAA,MAAA,EAAA,KAAA,EAAA,EAAA,6BAAA,WAAA,MAAA,EAAA,KAAA,EAAA,EAAA,6BArIJ,sFAyII,iBAAA,kBCvIJ,KACE,SAAA,SAIF,YACE,UAAA,KACA,YAAA,IAIF,QzBs/CA,QACA,QyBp/CE,iBAAA,QAIF,iBACE,cAAA,KAEF,4BACE,cAAA,EAGF,OACE,YAAA,KACA,WAAA,EAIF,2BACE,cAAA,EAGF,qCACE,MAAA,IzBk/CF,yCyB9+CA,yCAEE,YAAA,OAGF,yCACE,MAAA,MAGF,2CzB4+CA,2CyB1+CE,MAAA,MAGF,2CACE,MAAA,KAIF,UACE,QAAA,MACA,QAAA,KAAA,KACA,YAAA,IACA,MAAA,KACA,WAAA,OACA,iBAAA,QAEA,gBzBy+CF,gByBv+CI,MAAA,KACA,gBAAA,KACA,iBAAA,QAKJ,aACE,cACE,QAAA", "sourcesContent": ["/*!\n * Bootstrap Docs (https://getbootstrap.com/)\n * Copyright 2011-2019 Twitter, Inc.\n * Licensed under the Creative Commons Attribution 3.0 Unported License. For\n * details, see https://creativecommons.org/licenses/by/3.0/.\n */\n.hll {\n  background-color: #ffc;\n}\n.c {\n  color: #999;\n}\n.err {\n  color: #a00;\n  background-color: #faa;\n}\n.k {\n  color: #069;\n}\n.o {\n  color: #555;\n}\n.cm {\n  color: #999;\n}\n.cp {\n  color: #099;\n}\n.c1 {\n  color: #999;\n}\n.cs {\n  color: #999;\n}\n.gd {\n  background-color: #fcc;\n  border: 1px solid #c00;\n}\n.ge {\n  font-style: italic;\n}\n.gr {\n  color: #f00;\n}\n.gh {\n  color: #030;\n}\n.gi {\n  background-color: #cfc;\n  border: 1px solid #0c0;\n}\n.go {\n  color: #aaa;\n}\n.gp {\n  color: #009;\n}\n.gu {\n  color: #030;\n}\n.gt {\n  color: #9c6;\n}\n.kc {\n  color: #069;\n}\n.kd {\n  color: #069;\n}\n.kn {\n  color: #069;\n}\n.kp {\n  color: #069;\n}\n.kr {\n  color: #069;\n}\n.kt {\n  color: #078;\n}\n.m {\n  color: #f60;\n}\n.s {\n  color: #d44950;\n}\n.na {\n  color: #4f9fcf;\n}\n.nb {\n  color: #366;\n}\n.nc {\n  color: #0a8;\n}\n.no {\n  color: #360;\n}\n.nd {\n  color: #99f;\n}\n.ni {\n  color: #999;\n}\n.ne {\n  color: #c00;\n}\n.nf {\n  color: #c0f;\n}\n.nl {\n  color: #99f;\n}\n.nn {\n  color: #0cf;\n}\n.nt {\n  color: #2f6f9f;\n}\n.nv {\n  color: #033;\n}\n.ow {\n  color: #000;\n}\n.w {\n  color: #bbb;\n}\n.mf {\n  color: #f60;\n}\n.mh {\n  color: #f60;\n}\n.mi {\n  color: #f60;\n}\n.mo {\n  color: #f60;\n}\n.sb {\n  color: #c30;\n}\n.sc {\n  color: #c30;\n}\n.sd {\n  font-style: italic;\n  color: #c30;\n}\n.s2 {\n  color: #c30;\n}\n.se {\n  color: #c30;\n}\n.sh {\n  color: #c30;\n}\n.si {\n  color: #a00;\n}\n.sx {\n  color: #c30;\n}\n.sr {\n  color: #3aa;\n}\n.s1 {\n  color: #c30;\n}\n.ss {\n  color: #fc3;\n}\n.bp {\n  color: #366;\n}\n.vc {\n  color: #033;\n}\n.vg {\n  color: #033;\n}\n.vi {\n  color: #033;\n}\n.il {\n  color: #f60;\n}\n.css .o,\n.css .o + .nt,\n.css .nt + .nt {\n  color: #999;\n}\n.highlight {\n  padding: 9px 14px;\n  margin-bottom: 14px;\n  background-color: #f7f7f9;\n  border: 1px solid #e1e1e8;\n  border-radius: 4px;\n}\n.highlight pre {\n  padding: 0;\n  margin-top: 0;\n  margin-bottom: 0;\n  word-break: normal;\n  white-space: nowrap;\n  background-color: transparent;\n  border: 0;\n}\n.highlight pre code {\n  font-size: inherit;\n  color: #333;\n}\n.highlight pre code:first-child {\n  display: inline-block;\n  padding-right: 45px;\n}\n.language-bash:before {\n  color: #033;\n  content: \"$ \";\n  user-select: none;\n}\n/*!\n * IE10 viewport hack for Surface/desktop Windows 8 bug\n * Copyright 2014-2019 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n */\n@-ms-viewport {\n  width: device-width;\n}\n@-o-viewport {\n  width: device-width;\n}\n@viewport {\n  width: device-width;\n}\n.btn-outline {\n  color: #563d7c;\n  background-color: transparent;\n  border-color: #563d7c;\n}\n.btn-outline:hover,\n.btn-outline:focus,\n.btn-outline:active {\n  color: #fff;\n  background-color: #563d7c;\n  border-color: #563d7c;\n}\n.btn-outline-inverse {\n  color: #fff;\n  background-color: transparent;\n  border-color: #cdbfe3;\n}\n.btn-outline-inverse:hover,\n.btn-outline-inverse:focus,\n.btn-outline-inverse:active {\n  color: #563d7c;\n  text-shadow: none;\n  background-color: #fff;\n  border-color: #fff;\n}\n.bs-docs-booticon {\n  display: block;\n  font-weight: 500;\n  color: #fff;\n  text-align: center;\n  cursor: default;\n  background-color: #563d7c;\n  border-radius: 15%;\n}\n.bs-docs-booticon-sm {\n  width: 30px;\n  height: 30px;\n  font-size: 20px;\n  line-height: 28px;\n}\n.bs-docs-booticon-lg {\n  width: 144px;\n  height: 144px;\n  font-size: 108px;\n  line-height: 140px;\n}\n.bs-docs-booticon-inverse {\n  color: #563d7c;\n  background-color: #fff;\n}\n.bs-docs-booticon-outline {\n  background-color: transparent;\n  border: 1px solid #cdbfe3;\n}\n#skippy {\n  display: block;\n  padding: 1em;\n  color: #fff;\n  background-color: #6f5499;\n  outline: 0;\n}\n#skippy .skiplink-text {\n  padding: 0.5em;\n  outline: 1px dotted;\n}\n#content:focus {\n  outline: none;\n}\n.bs-docs-nav {\n  margin-bottom: 0;\n  background-color: #fff;\n  border-bottom: 0;\n}\n.bs-docs-nav .bs-nav-b {\n  display: none;\n}\n.bs-docs-nav .navbar-brand,\n.bs-docs-nav .navbar-nav > li > a {\n  font-weight: 500;\n  color: #563d7c;\n}\n.bs-docs-nav .navbar-nav > li > a {\n  padding-right: 10px;\n  padding-left: 10px;\n}\n.bs-docs-nav .navbar-nav > li > a:hover,\n.bs-docs-nav .navbar-nav > .active > a,\n.bs-docs-nav .navbar-nav > .active > a:hover {\n  color: #463265;\n  background-color: #f9f9f9;\n}\n.bs-docs-nav .navbar-toggle .icon-bar {\n  background-color: #563d7c;\n}\n.bs-docs-nav .navbar-header .navbar-toggle {\n  border-color: #fff;\n}\n.bs-docs-nav .navbar-header .navbar-toggle:hover,\n.bs-docs-nav .navbar-header .navbar-toggle:focus {\n  background-color: #f9f9f9;\n  border-color: #f9f9f9;\n}\n@media (min-width: 768px) and (max-width: 992px) {\n  .bs-docs-nav .navbar-right {\n    display: none;\n  }\n}\n.bs-docs-footer {\n  padding-top: 50px;\n  padding-bottom: 50px;\n  margin-top: 100px;\n  color: #99979c;\n  text-align: center;\n  background-color: #2a2730;\n}\n.bs-docs-footer a {\n  color: #fff;\n}\n.bs-docs-footer-links {\n  padding-left: 0;\n  margin-bottom: 20px;\n}\n.bs-docs-footer-links li {\n  display: inline-block;\n}\n.bs-docs-footer-links li + li {\n  margin-left: 15px;\n}\n@media (min-width: 768px) {\n  .bs-docs-footer {\n    text-align: left;\n  }\n  .bs-docs-footer p {\n    margin-bottom: 0;\n  }\n}\n.bs-docs-masthead,\n.bs-docs-header {\n  position: relative;\n  padding: 30px 0;\n  color: #cdbfe3;\n  text-align: center;\n  text-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);\n  background-color: #6f5499;\n  background-image: -webkit-gradient(linear, left top, left bottom, from(#563d7c), to(#6f5499));\n  background-image: -webkit-linear-gradient(top, #563d7c 0%, #6f5499 100%);\n  background-image: -o-linear-gradient(top, #563d7c 0%, #6f5499 100%);\n  background-image: linear-gradient(to bottom, #563d7c 0%, #6f5499 100%);\n  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=\"#563d7c\", endColorstr=\"#6F5499\", GradientType=0);\n  background-repeat: repeat-x;\n}\n.bs-docs-masthead .bs-docs-booticon {\n  margin: 0 auto 30px;\n}\n.bs-docs-masthead h1 {\n  font-weight: 300;\n  line-height: 1;\n  color: #fff;\n}\n.bs-docs-masthead .lead {\n  margin: 0 auto 30px;\n  font-size: 20px;\n  color: #fff;\n}\n.bs-docs-masthead .version {\n  margin-top: -15px;\n  margin-bottom: 30px;\n  color: #9783b9;\n}\n.bs-docs-masthead .btn {\n  width: 100%;\n  padding: 15px 30px;\n  font-size: 20px;\n}\n@media (min-width: 480px) {\n  .bs-docs-masthead .btn {\n    width: auto;\n  }\n}\n@media (min-width: 768px) {\n  .bs-docs-masthead {\n    padding: 80px 0;\n  }\n  .bs-docs-masthead h1 {\n    font-size: 60px;\n  }\n  .bs-docs-masthead .lead {\n    font-size: 24px;\n  }\n}\n@media (min-width: 992px) {\n  .bs-docs-masthead .lead {\n    width: 80%;\n    font-size: 30px;\n  }\n}\n.bs-docs-header {\n  margin-bottom: 40px;\n  font-size: 20px;\n}\n.bs-docs-header h1 {\n  margin-top: 0;\n  color: #fff;\n}\n.bs-docs-header p {\n  margin-bottom: 0;\n  font-weight: 300;\n  line-height: 1.4;\n}\n.bs-docs-header .container {\n  position: relative;\n}\n@media (min-width: 768px) {\n  .bs-docs-header {\n    padding-top: 60px;\n    padding-bottom: 60px;\n    font-size: 24px;\n    text-align: left;\n  }\n  .bs-docs-header h1 {\n    font-size: 60px;\n    line-height: 1;\n  }\n}\n@media (min-width: 992px) {\n  .bs-docs-header h1,\n  .bs-docs-header p {\n    margin-right: 380px;\n  }\n}\n#carbonads {\n  display: block;\n  padding: 15px 15px 15px 160px;\n  margin: 50px -15px -30px;\n  overflow: hidden;\n  font-size: 13px;\n  line-height: 1.5;\n  text-align: left;\n  border: solid #866ab3;\n  border-width: 1px 0 0;\n}\n#carbonads a {\n  color: #fff;\n  text-decoration: none;\n}\n@media (min-width: 768px) {\n  #carbonads {\n    max-width: 330px;\n    margin: 50px auto 0;\n    border-width: 1px;\n    border-radius: 4px;\n  }\n}\n@media (min-width: 992px) {\n  #carbonads {\n    position: absolute;\n    top: 0;\n    right: 15px;\n    margin-top: 0;\n  }\n  .bs-docs-masthead #carbonads {\n    position: static;\n  }\n}\n.carbon-img {\n  float: left;\n  margin-left: -145px;\n}\n.carbon-poweredby {\n  display: block;\n  color: #cdbfe3 !important;\n}\n.bs-docs-featurette {\n  padding-top: 40px;\n  padding-bottom: 40px;\n  font-size: 16px;\n  line-height: 1.5;\n  color: #555;\n  text-align: center;\n  background-color: #fff;\n  border-bottom: 1px solid #e5e5e5;\n}\n.bs-docs-featurette + .bs-docs-footer {\n  margin-top: 0;\n  border-top: 0;\n}\n.bs-docs-featurette-title {\n  margin-bottom: 5px;\n  font-size: 30px;\n  font-weight: 400;\n  color: #333;\n}\n.half-rule {\n  width: 100px;\n  margin: 40px auto;\n}\n.bs-docs-featurette h3 {\n  margin-bottom: 5px;\n  font-weight: 400;\n  color: #333;\n}\n.bs-docs-featurette-img {\n  display: block;\n  margin-bottom: 20px;\n  color: #333;\n}\n.bs-docs-featurette-img:hover {\n  color: #337ab7;\n  text-decoration: none;\n}\n.bs-docs-featurette-img img {\n  display: block;\n  margin-bottom: 15px;\n}\n@media (min-width: 480px) {\n  .bs-docs-featurette .img-responsive {\n    margin-top: 30px;\n  }\n}\n@media (min-width: 768px) {\n  .bs-docs-featurette {\n    padding-top: 100px;\n    padding-bottom: 100px;\n  }\n  .bs-docs-featurette-title {\n    font-size: 40px;\n  }\n  .bs-docs-featurette .lead {\n    max-width: 80%;\n    margin-right: auto;\n    margin-left: auto;\n  }\n  .bs-docs-featurette .img-responsive {\n    margin-top: 0;\n  }\n}\n.bs-docs-featured-sites {\n  margin-right: -1px;\n  margin-left: -1px;\n}\n.bs-docs-featured-sites .col-xs-8 {\n  padding: 1px;\n}\n.bs-docs-featured-sites .img-responsive {\n  margin-top: 0;\n}\n@media (min-width: 768px) {\n  .bs-docs-featured-sites .col-sm-4:first-child img {\n    border-top-left-radius: 4px;\n    border-bottom-left-radius: 4px;\n  }\n  .bs-docs-featured-sites .col-sm-4:last-child img {\n    border-top-right-radius: 4px;\n    border-bottom-right-radius: 4px;\n  }\n}\n.bs-examples .thumbnail {\n  margin-bottom: 10px;\n}\n.bs-examples h4 {\n  margin-bottom: 5px;\n}\n.bs-examples p {\n  margin-bottom: 20px;\n}\n@media (max-width: 480px) {\n  .bs-examples {\n    margin-right: -10px;\n    margin-left: -10px;\n  }\n  .bs-examples > [class^=\"col-\"] {\n    padding-right: 10px;\n    padding-left: 10px;\n  }\n}\n.bs-docs-sidebar.affix {\n  position: static;\n}\n@media (min-width: 768px) {\n  .bs-docs-sidebar {\n    padding-left: 20px;\n  }\n}\n.bs-docs-search {\n  margin-bottom: 20px;\n  margin-left: 20px;\n}\n.bs-docs-sidenav {\n  margin-top: 20px;\n  margin-bottom: 20px;\n}\n.bs-docs-sidebar .nav > li > a {\n  display: block;\n  padding: 4px 20px;\n  font-size: 13px;\n  font-weight: 500;\n  color: #767676;\n}\n.bs-docs-sidebar .nav > li > a:hover,\n.bs-docs-sidebar .nav > li > a:focus {\n  padding-left: 19px;\n  color: #563d7c;\n  text-decoration: none;\n  background-color: transparent;\n  border-left: 1px solid #563d7c;\n}\n.bs-docs-sidebar .nav > .active > a,\n.bs-docs-sidebar .nav > .active:hover > a,\n.bs-docs-sidebar .nav > .active:focus > a {\n  padding-left: 18px;\n  font-weight: 700;\n  color: #563d7c;\n  background-color: transparent;\n  border-left: 2px solid #563d7c;\n}\n.bs-docs-sidebar .nav .nav {\n  display: none;\n  padding-bottom: 10px;\n}\n.bs-docs-sidebar .nav .nav > li > a {\n  padding-top: 1px;\n  padding-bottom: 1px;\n  padding-left: 30px;\n  font-size: 12px;\n  font-weight: 400;\n}\n.bs-docs-sidebar .nav .nav > li > a:hover,\n.bs-docs-sidebar .nav .nav > li > a:focus {\n  padding-left: 29px;\n}\n.bs-docs-sidebar .nav .nav > .active > a,\n.bs-docs-sidebar .nav .nav > .active:hover > a,\n.bs-docs-sidebar .nav .nav > .active:focus > a {\n  padding-left: 28px;\n  font-weight: 500;\n}\n.back-to-top,\n.bs-docs-theme-toggle {\n  display: none;\n  padding: 4px 10px;\n  margin-top: 10px;\n  margin-left: 10px;\n  font-size: 12px;\n  font-weight: 500;\n  color: #999;\n}\n.back-to-top:hover,\n.bs-docs-theme-toggle:hover {\n  color: #563d7c;\n  text-decoration: none;\n}\n.bs-docs-theme-toggle {\n  margin-top: 0;\n}\n@media (min-width: 768px) {\n  .back-to-top,\n  .bs-docs-theme-toggle {\n    display: block;\n  }\n}\n@media (min-width: 992px) {\n  .bs-docs-sidebar .nav > .active > ul {\n    display: block;\n  }\n  .bs-docs-sidebar.affix,\n  .bs-docs-sidebar.affix-bottom {\n    width: 213px;\n  }\n  .bs-docs-sidebar.affix {\n    position: fixed;\n    top: 20px;\n  }\n  .bs-docs-sidebar.affix-bottom {\n    position: absolute;\n  }\n  .bs-docs-sidebar.affix-bottom .bs-docs-sidenav,\n  .bs-docs-sidebar.affix .bs-docs-sidenav {\n    margin-top: 0;\n    margin-bottom: 0;\n  }\n}\n@media (min-width: 1200px) {\n  .bs-docs-sidebar.affix-bottom,\n  .bs-docs-sidebar.affix {\n    width: 263px;\n  }\n}\n.show-grid {\n  margin-bottom: 15px;\n}\n.show-grid [class^=\"col-\"] {\n  padding-top: 10px;\n  padding-bottom: 10px;\n  background-color: #eee;\n  background-color: rgba(86, 61, 124, 0.15);\n  border: 1px solid #ddd;\n  border: 1px solid rgba(86, 61, 124, 0.2);\n}\n.bs-example {\n  position: relative;\n  padding: 45px 15px 15px;\n  margin: 0 -15px 15px;\n  border-color: #e5e5e5 #eee #eee;\n  border-style: solid;\n  border-width: 1px 0;\n  box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.05);\n}\n.bs-example:after {\n  position: absolute;\n  top: 15px;\n  left: 15px;\n  font-size: 12px;\n  font-weight: 700;\n  color: #959595;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n  content: \"Example\";\n}\n.bs-example-padded-bottom {\n  padding-bottom: 24px;\n}\n.bs-example + .highlight,\n.bs-example + .bs-clipboard + .highlight {\n  margin: -15px -15px 15px;\n  border-width: 0 0 1px;\n  border-radius: 0;\n}\n@media (min-width: 768px) {\n  .bs-example {\n    margin-right: 0;\n    margin-left: 0;\n    background-color: #fff;\n    border-color: #ddd;\n    border-width: 1px;\n    border-radius: 4px 4px 0 0;\n    box-shadow: none;\n  }\n  .bs-example + .highlight,\n  .bs-example + .bs-clipboard + .highlight {\n    margin-top: -16px;\n    margin-right: 0;\n    margin-left: 0;\n    border-width: 1px;\n    border-bottom-right-radius: 4px;\n    border-bottom-left-radius: 4px;\n  }\n  .bs-example + .bs-clipboard .btn-clipboard {\n    top: -15px;\n    border-top-right-radius: 0;\n  }\n  .bs-example-standalone {\n    border-radius: 4px;\n  }\n}\n.bs-example .container {\n  width: auto;\n}\n.bs-example > p:last-child,\n.bs-example > ul:last-child,\n.bs-example > ol:last-child,\n.bs-example > blockquote:last-child,\n.bs-example > .form-control:last-child,\n.bs-example > .table:last-child,\n.bs-example > .navbar:last-child,\n.bs-example > .jumbotron:last-child,\n.bs-example > .alert:last-child,\n.bs-example > .panel:last-child,\n.bs-example > .list-group:last-child,\n.bs-example > .well:last-child,\n.bs-example > .progress:last-child,\n.bs-example > .table-responsive:last-child > .table {\n  margin-bottom: 0;\n}\n.bs-example > p > .close {\n  float: none;\n}\n.bs-example-type .table .type-info {\n  color: #767676;\n  vertical-align: middle;\n}\n.bs-example-type .table td {\n  padding: 15px 0;\n  border-color: #eee;\n}\n.bs-example-type .table tr:first-child td {\n  border-top: 0;\n}\n.bs-example-type h1,\n.bs-example-type h2,\n.bs-example-type h3,\n.bs-example-type h4,\n.bs-example-type h5,\n.bs-example-type h6 {\n  margin: 0;\n}\n.bs-example-bg-classes p {\n  padding: 15px;\n}\n.bs-example > .img-circle,\n.bs-example > .img-rounded,\n.bs-example > .img-thumbnail {\n  margin: 5px;\n}\n.bs-example > .table-responsive > .table {\n  background-color: #fff;\n}\n.bs-example > .btn,\n.bs-example > .btn-group {\n  margin-top: 5px;\n  margin-bottom: 5px;\n}\n.bs-example > .btn-toolbar + .btn-toolbar {\n  margin-top: 10px;\n}\n.bs-example-control-sizing select,\n.bs-example-control-sizing input[type=\"text\"] + input[type=\"text\"] {\n  margin-top: 10px;\n}\n.bs-example-form .input-group {\n  margin-bottom: 10px;\n}\n.bs-example > textarea.form-control {\n  resize: vertical;\n}\n.bs-example > .list-group {\n  max-width: 400px;\n}\n.bs-example .navbar:last-child {\n  margin-bottom: 0;\n}\n.bs-navbar-top-example,\n.bs-navbar-bottom-example {\n  z-index: 1;\n  padding: 0;\n  overflow: hidden;\n}\n.bs-navbar-top-example .navbar-header,\n.bs-navbar-bottom-example .navbar-header {\n  margin-left: 0;\n}\n.bs-navbar-top-example .navbar-fixed-top,\n.bs-navbar-bottom-example .navbar-fixed-bottom {\n  position: relative;\n  margin-right: 0;\n  margin-left: 0;\n}\n.bs-navbar-top-example {\n  padding-bottom: 45px;\n}\n.bs-navbar-top-example:after {\n  top: auto;\n  bottom: 15px;\n}\n.bs-navbar-top-example .navbar-fixed-top {\n  top: -1px;\n}\n.bs-navbar-bottom-example {\n  padding-top: 45px;\n}\n.bs-navbar-bottom-example .navbar-fixed-bottom {\n  bottom: -1px;\n}\n.bs-navbar-bottom-example .navbar {\n  margin-bottom: 0;\n}\n@media (min-width: 768px) {\n  .bs-navbar-top-example .navbar-fixed-top,\n  .bs-navbar-bottom-example .navbar-fixed-bottom {\n    position: absolute;\n  }\n}\n.bs-example .pagination {\n  margin-top: 10px;\n  margin-bottom: 10px;\n}\n.bs-example > .pager {\n  margin-top: 0;\n}\n.bs-example-modal {\n  background-color: #f5f5f5;\n}\n.bs-example-modal .modal {\n  position: relative;\n  top: auto;\n  right: auto;\n  bottom: auto;\n  left: auto;\n  z-index: 1;\n  display: block;\n}\n.bs-example-modal .modal-dialog {\n  left: auto;\n  margin-right: auto;\n  margin-left: auto;\n}\n.bs-example > .dropdown > .dropdown-toggle {\n  float: left;\n}\n.bs-example > .dropdown > .dropdown-menu {\n  position: static;\n  display: block;\n  margin-bottom: 5px;\n  clear: left;\n}\n.bs-example-tabs .nav-tabs {\n  margin-bottom: 15px;\n}\n.bs-example-tooltips {\n  text-align: center;\n}\n.bs-example-tooltips > .btn {\n  margin-top: 5px;\n  margin-bottom: 5px;\n}\n.bs-example-tooltip .tooltip {\n  position: relative;\n  display: inline-block;\n  margin: 10px 20px;\n  opacity: 1;\n}\n.bs-example-popover {\n  padding-bottom: 24px;\n  background-color: #f9f9f9;\n}\n.bs-example-popover .popover {\n  position: relative;\n  display: block;\n  float: left;\n  width: 260px;\n  margin: 20px;\n}\n.scrollspy-example {\n  position: relative;\n  height: 200px;\n  margin-top: 10px;\n  overflow: auto;\n}\n.bs-example > .nav-pills-stacked-example {\n  max-width: 300px;\n}\n#collapseExample .well {\n  margin-bottom: 0;\n}\n#focusedInput {\n  border-color: #cccccc;\n  border-color: rgba(82, 168, 236, 0.8);\n  outline: 0;\n  outline: thin dotted \\9;\n  box-shadow: 0 0 8px rgba(82, 168, 236, 0.6);\n}\n.bs-callout {\n  padding: 20px;\n  margin: 20px 0;\n  border: 1px solid #eee;\n  border-left-width: 5px;\n  border-radius: 3px;\n}\n.bs-callout h4 {\n  margin-top: 0;\n  margin-bottom: 5px;\n}\n.bs-callout p:last-child {\n  margin-bottom: 0;\n}\n.bs-callout code {\n  border-radius: 3px;\n}\n.bs-callout + .bs-callout {\n  margin-top: -5px;\n}\n.bs-callout-danger {\n  border-left-color: #ce4844;\n}\n.bs-callout-danger h4 {\n  color: #ce4844;\n}\n.bs-callout-warning {\n  border-left-color: #aa6708;\n}\n.bs-callout-warning h4 {\n  color: #aa6708;\n}\n.bs-callout-info {\n  border-left-color: #1b809e;\n}\n.bs-callout-info h4 {\n  color: #1b809e;\n}\n.color-swatches {\n  margin: 0 -5px;\n  overflow: hidden;\n  /* clearfix */\n}\n.color-swatch {\n  float: left;\n  width: 60px;\n  height: 60px;\n  margin: 0 5px;\n  border-radius: 3px;\n}\n@media (min-width: 768px) {\n  .color-swatch {\n    width: 100px;\n    height: 100px;\n  }\n}\n.color-swatches .gray-darker {\n  background-color: #222;\n}\n.color-swatches .gray-dark {\n  background-color: #333;\n}\n.color-swatches .gray {\n  background-color: #555;\n}\n.color-swatches .gray-light {\n  background-color: #999;\n}\n.color-swatches .gray-lighter {\n  background-color: #eee;\n}\n.color-swatches .brand-primary {\n  background-color: #337ab7;\n}\n.color-swatches .brand-success {\n  background-color: #5cb85c;\n}\n.color-swatches .brand-warning {\n  background-color: #f0ad4e;\n}\n.color-swatches .brand-danger {\n  background-color: #d9534f;\n}\n.color-swatches .brand-info {\n  background-color: #5bc0de;\n}\n.color-swatches .bs-purple {\n  background-color: #563d7c;\n}\n.color-swatches .bs-purple-light {\n  background-color: #c7bfd3;\n}\n.color-swatches .bs-purple-lighter {\n  background-color: #e5e1ea;\n}\n.color-swatches .bs-gray {\n  background-color: #f9f9f9;\n}\n.bs-team .team-member {\n  line-height: 32px;\n  color: #555;\n}\n.bs-team .team-member:hover {\n  color: #333;\n  text-decoration: none;\n}\n.bs-team .github-btn {\n  float: right;\n  width: 180px;\n  height: 20px;\n  margin-top: 6px;\n  border: none;\n}\n.bs-team img {\n  float: left;\n  width: 32px;\n  margin-right: 10px;\n  border-radius: 4px;\n}\n.table-responsive .highlight pre {\n  white-space: normal;\n}\n.bs-table th small,\n.responsive-utilities th small {\n  display: block;\n  font-weight: 400;\n  color: #999;\n}\n.responsive-utilities tbody th {\n  font-weight: 400;\n}\n.responsive-utilities td {\n  text-align: center;\n}\n.responsive-utilities td.is-visible {\n  color: #468847;\n  background-color: #dff0d8 !important;\n}\n.responsive-utilities td.is-hidden {\n  color: #ccc;\n  background-color: #f9f9f9 !important;\n}\n.responsive-utilities-test {\n  margin-top: 5px;\n}\n.responsive-utilities-test .col-xs-6 {\n  margin-bottom: 10px;\n}\n.responsive-utilities-test span {\n  display: block;\n  padding: 15px 10px;\n  font-size: 14px;\n  font-weight: 700;\n  line-height: 1.1;\n  text-align: center;\n  border-radius: 4px;\n}\n.visible-on .col-xs-6 .hidden-xs,\n.visible-on .col-xs-6 .hidden-sm,\n.visible-on .col-xs-6 .hidden-md,\n.visible-on .col-xs-6 .hidden-lg,\n.hidden-on .col-xs-6 .hidden-xs,\n.hidden-on .col-xs-6 .hidden-sm,\n.hidden-on .col-xs-6 .hidden-md,\n.hidden-on .col-xs-6 .hidden-lg {\n  color: #999;\n  border: 1px solid #ddd;\n}\n.visible-on .col-xs-6 .visible-xs-block,\n.visible-on .col-xs-6 .visible-sm-block,\n.visible-on .col-xs-6 .visible-md-block,\n.visible-on .col-xs-6 .visible-lg-block,\n.hidden-on .col-xs-6 .visible-xs-block,\n.hidden-on .col-xs-6 .visible-sm-block,\n.hidden-on .col-xs-6 .visible-md-block,\n.hidden-on .col-xs-6 .visible-lg-block {\n  color: #468847;\n  background-color: #dff0d8;\n  border: 1px solid #d6e9c6;\n}\n.bs-glyphicons {\n  margin: 0 -10px 20px;\n  overflow: hidden;\n}\n.bs-glyphicons-list {\n  padding-left: 0;\n  list-style: none;\n}\n.bs-glyphicons li {\n  float: left;\n  width: 25%;\n  height: 115px;\n  padding: 10px;\n  font-size: 10px;\n  line-height: 1.4;\n  text-align: center;\n  background-color: #f9f9f9;\n  border: 1px solid #fff;\n}\n.bs-glyphicons .glyphicon {\n  margin-top: 5px;\n  margin-bottom: 10px;\n  font-size: 24px;\n}\n.bs-glyphicons .glyphicon-class {\n  display: block;\n  text-align: center;\n  word-wrap: break-word;\n}\n.bs-glyphicons li:hover {\n  color: #fff;\n  background-color: #563d7c;\n}\n@media (min-width: 768px) {\n  .bs-glyphicons {\n    margin-right: 0;\n    margin-left: 0;\n  }\n  .bs-glyphicons li {\n    width: 12.5%;\n    font-size: 12px;\n  }\n}\n.bs-customizer .toggle {\n  float: right;\n  margin-top: 25px;\n}\n.bs-customizer label {\n  margin-top: 10px;\n  font-weight: 500;\n  color: #555;\n}\n.bs-customizer h2 {\n  padding-top: 30px;\n  margin-top: 0;\n  margin-bottom: 5px;\n}\n.bs-customizer h3 {\n  margin-bottom: 0;\n}\n.bs-customizer h4 {\n  margin-top: 15px;\n  margin-bottom: 0;\n}\n.bs-customizer .bs-callout h4 {\n  margin-top: 0;\n  margin-bottom: 5px;\n}\n.bs-customizer input[type=\"text\"] {\n  font-family: Menlo, Monaco, Consolas, \"Courier New\", monospace;\n  background-color: #fafafa;\n}\n.bs-customizer .help-block {\n  margin-bottom: 5px;\n  font-size: 12px;\n}\n#less-section label {\n  font-weight: 400;\n}\n.bs-customize-download .btn-outline {\n  padding: 20px;\n}\n.bs-customizer-alert {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 1030;\n  padding: 15px 0;\n  color: #fff;\n  background-color: #d9534f;\n  border-bottom: 1px solid #b94441;\n  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.25);\n}\n.bs-customizer-alert .close {\n  margin-top: -4px;\n  font-size: 24px;\n}\n.bs-customizer-alert p {\n  margin-bottom: 0;\n}\n.bs-customizer-alert .glyphicon {\n  margin-right: 5px;\n}\n.bs-customizer-alert pre {\n  margin: 10px 0 0;\n  color: #fff;\n  background-color: #a83c3a;\n  border-color: #973634;\n  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05), 0 1px 0 rgba(255, 255, 255, 0.1);\n}\n.bs-dropzone {\n  position: relative;\n  padding: 20px;\n  margin-bottom: 20px;\n  color: #777;\n  text-align: center;\n  border: 2px dashed #eee;\n  border-radius: 4px;\n}\n.bs-dropzone .import-header {\n  margin-bottom: 5px;\n}\n.bs-dropzone .glyphicon-folder-open {\n  font-size: 40px;\n}\n.bs-dropzone hr {\n  width: 100px;\n}\n.bs-dropzone .lead {\n  margin-bottom: 10px;\n  font-weight: 400;\n  color: #333;\n}\n#import-manual-trigger {\n  cursor: pointer;\n}\n.bs-dropzone p:last-child {\n  margin-bottom: 0;\n}\n.bs-brand-logos {\n  display: table;\n  width: 100%;\n  margin-bottom: 15px;\n  overflow: hidden;\n  color: #563d7c;\n  background-color: #f9f9f9;\n  border-radius: 4px;\n}\n.bs-brand-item {\n  padding: 60px 0;\n  text-align: center;\n}\n.bs-brand-item + .bs-brand-item {\n  border-top: 1px solid #fff;\n}\n.bs-brand-logos .inverse {\n  color: #fff;\n  background-color: #563d7c;\n}\n.bs-brand-item h1,\n.bs-brand-item h3 {\n  margin-top: 0;\n  margin-bottom: 0;\n}\n.bs-brand-item .bs-docs-booticon {\n  margin-right: auto;\n  margin-left: auto;\n}\n.bs-brand-item .glyphicon {\n  width: 30px;\n  height: 30px;\n  margin: 10px auto -10px;\n  line-height: 30px;\n  color: #fff;\n  border-radius: 50%;\n}\n.bs-brand-item .glyphicon-ok {\n  background-color: #5cb85c;\n}\n.bs-brand-item .glyphicon-remove {\n  background-color: #d9534f;\n}\n@media (min-width: 768px) {\n  .bs-brand-item {\n    display: table-cell;\n    width: 1%;\n  }\n  .bs-brand-item + .bs-brand-item {\n    border-top: 0;\n    border-left: 1px solid #fff;\n  }\n  .bs-brand-item h1 {\n    font-size: 60px;\n  }\n}\n.bs-clipboard {\n  position: relative;\n  display: none;\n  float: right;\n}\n.bs-clipboard + .highlight {\n  margin-top: 0;\n}\n.btn-clipboard {\n  position: absolute;\n  top: 0;\n  right: 0;\n  z-index: 10;\n  display: block;\n  padding: 4px 8px;\n  font-size: 12px;\n  color: #818a91;\n  cursor: pointer;\n  background-color: transparent;\n  border: 0;\n  border-top-right-radius: 4px;\n  border-bottom-left-radius: 4px;\n}\n.btn-clipboard:hover {\n  color: #fff;\n  background-color: #027de7;\n}\n@media (min-width: 768px) {\n  .bs-clipboard {\n    display: block;\n  }\n}\n.anchorjs-link {\n  color: inherit;\n}\n@media (max-width: 480px) {\n  .anchorjs-link {\n    display: none;\n  }\n}\n*:hover > .anchorjs-link {\n  opacity: 0.75;\n  transition: color 0.16s linear;\n}\n*:hover > .anchorjs-link:hover,\n.anchorjs-link:focus {\n  text-decoration: none;\n  opacity: 1;\n}\n.algolia-autocomplete {\n  display: block !important;\n}\n.algolia-autocomplete .ds-dropdown-menu {\n  width: 100%;\n  min-width: 0 !important;\n  max-width: none !important;\n  padding: 10px 0 !important;\n  background-color: #fff;\n  background-clip: padding-box;\n  border: 1px solid #ddd;\n  border: 1px solid rgba(0, 0, 0, 0.1);\n  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.175);\n}\n@media (min-width: 768px) {\n  .algolia-autocomplete .ds-dropdown-menu {\n    width: 175%;\n  }\n}\n.algolia-autocomplete .ds-dropdown-menu:before {\n  display: none !important;\n}\n.algolia-autocomplete .ds-dropdown-menu [class^=\"ds-dataset-\"] {\n  padding: 0 !important;\n  overflow: visible !important;\n  background-color: transparent !important;\n  border: 0 !important;\n}\n.algolia-autocomplete .ds-dropdown-menu .ds-suggestions {\n  margin-top: 0 !important;\n}\n.algolia-autocomplete .ds-dropdown-menu .ds-input {\n  box-shadow: none;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion {\n  padding: 0 !important;\n  overflow: visible !important;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion--category-header {\n  padding: 2px 15px !important;\n  margin-top: 0 !important;\n  font-size: 13px !important;\n  font-weight: 500 !important;\n  color: #7952b3 !important;\n  border-bottom: 0 !important;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion--wrapper {\n  float: none !important;\n  padding-top: 0 !important;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion--subcategory-column {\n  float: none !important;\n  width: auto !important;\n  padding: 0 !important;\n  text-align: left !important;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion--content {\n  float: none !important;\n  width: auto !important;\n  padding: 0 !important;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion--content:before {\n  display: none !important;\n}\n.algolia-autocomplete .ds-suggestion:not(:first-child) .algolia-docsearch-suggestion--category-header {\n  padding-top: 10px !important;\n  margin-top: 10px !important;\n  border-top: 1px solid #eee;\n}\n.algolia-autocomplete .ds-suggestion .algolia-docsearch-suggestion--subcategory-column {\n  display: none !important;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion--title {\n  display: block;\n  padding: 4px 15px !important;\n  margin-bottom: 0 !important;\n  font-size: 13px !important;\n  font-weight: 400 !important;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion--text {\n  padding: 0 15px 8px !important;\n  margin-top: -4px;\n  font-size: 13px !important;\n  font-weight: 400;\n  line-height: 1.25 !important;\n}\n.algolia-autocomplete .algolia-docsearch-footer {\n  float: none !important;\n  width: auto !important;\n  height: auto !important;\n  padding: 10px 15px 0;\n  font-size: 10px !important;\n  line-height: 1 !important;\n  color: #767676 !important;\n  border-top: 1px solid #eee;\n}\n.algolia-autocomplete .algolia-docsearch-footer--logo {\n  display: inline !important;\n  overflow: visible !important;\n  color: inherit !important;\n  text-indent: 0 !important;\n  background: none !important;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion--highlight {\n  color: #5f2dab;\n  background-color: #eee;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion--text .algolia-docsearch-suggestion--highlight {\n  box-shadow: inset 0 -2px 0 0 rgba(95, 45, 171, 0.5) !important;\n}\n.algolia-autocomplete .ds-suggestion.ds-cursor .algolia-docsearch-suggestion--content {\n  background-color: #e5e5e5 !important;\n}\nbody {\n  position: relative;\n}\n.table code {\n  font-size: 13px;\n  font-weight: 400;\n}\nh2 code,\nh3 code,\nh4 code {\n  background-color: inherit;\n}\n.bs-docs-section {\n  margin-bottom: 60px;\n}\n.bs-docs-section:last-child {\n  margin-bottom: 0;\n}\nh1[id] {\n  padding-top: 20px;\n  margin-top: 0;\n}\n.bs-docs-browser-bugs td p {\n  margin-bottom: 0;\n}\n.bs-docs-browser-bugs th:first-child {\n  width: 18%;\n}\n.bs-events-table > thead > tr > th:first-child,\n.bs-events-table > tbody > tr > td:first-child {\n  white-space: nowrap;\n}\n.bs-events-table > thead > tr > th:first-child {\n  width: 150px;\n}\n.js-options-table > thead > tr > th:nth-child(1),\n.js-options-table > thead > tr > th:nth-child(2) {\n  width: 100px;\n}\n.js-options-table > thead > tr > th:nth-child(3) {\n  width: 50px;\n}\n.v4-tease {\n  display: block;\n  padding: 15px 20px;\n  font-weight: 700;\n  color: #fff;\n  text-align: center;\n  background-color: #0275d8;\n}\n.v4-tease:focus,\n.v4-tease:hover {\n  color: #fff;\n  text-decoration: none;\n  background-color: #0269c2;\n}\n/* Nullify ill-advised printing of hrefs; see #18711 */\n@media print {\n  a[href]:after {\n    content: \"\" !important;\n  }\n}\n/*# sourceMappingURL=docs.css.map */", "// stylelint-disable declaration-block-single-line-max-declarations\n\n.hll { background-color: #ffc; }\n.c { color: #999; }\n.err { color: #a00; background-color: #faa; }\n.k { color: #069; }\n.o { color: #555; }\n.cm { color: #999; }\n.cp { color: #099; }\n.c1 { color: #999; }\n.cs { color: #999; }\n.gd { background-color: #fcc; border: 1px solid #c00; }\n.ge { font-style: italic; }\n.gr { color: #f00; }\n.gh { color: #030; }\n.gi { background-color: #cfc; border: 1px solid #0c0; }\n.go { color: #aaa; }\n.gp { color: #009; }\n.gu { color: #030; }\n.gt { color: #9c6; }\n.kc { color: #069; }\n.kd { color: #069; }\n.kn { color: #069; }\n.kp { color: #069; }\n.kr { color: #069; }\n.kt { color: #078; }\n.m { color: #f60; }\n.s { color: #d44950; }\n.na { color: #4f9fcf; }\n.nb { color: #366; }\n.nc { color: #0a8; }\n.no { color: #360; }\n.nd { color: #99f; }\n.ni { color: #999; }\n.ne { color: #c00; }\n.nf { color: #c0f; }\n.nl { color: #99f; }\n.nn { color: #0cf; }\n.nt { color: #2f6f9f; }\n.nv { color: #033; }\n.ow { color: #000; }\n.w { color: #bbb; }\n.mf { color: #f60; }\n.mh { color: #f60; }\n.mi { color: #f60; }\n.mo { color: #f60; }\n.sb { color: #c30; }\n.sc { color: #c30; }\n.sd { font-style: italic; color: #c30; }\n.s2 { color: #c30; }\n.se { color: #c30; }\n.sh { color: #c30; }\n.si { color: #a00; }\n.sx { color: #c30; }\n.sr { color: #3aa; }\n.s1 { color: #c30; }\n.ss { color: #fc3; }\n.bp { color: #366; }\n.vc { color: #033; }\n.vg { color: #033; }\n.vi { color: #033; }\n.il { color: #f60; }\n\n.css .o,\n.css .o + .nt,\n.css .nt + .nt { color: #999; }\n\n\n//\n// Docs additions\n//\n\n.highlight {\n  padding: 9px 14px;\n  margin-bottom: 14px;\n  background-color: #f7f7f9;\n  border: 1px solid #e1e1e8;\n  border-radius: 4px;\n}\n.highlight pre {\n  padding: 0;\n  margin-top: 0;\n  margin-bottom: 0;\n  word-break: normal;\n  white-space: nowrap;\n  background-color: transparent;\n  border: 0;\n}\n.highlight pre code {\n  font-size: inherit;\n  color: #333; // Effectively the base text color\n}\n.highlight pre code:first-child {\n  display: inline-block;\n  padding-right: 45px;\n}\n\n.language-bash:before {\n  color: #033;\n  content: \"$ \";\n  user-select: none;\n}\n", "/*!\n * Bootstrap Docs (https://getbootstrap.com/)\n * Copyright 2011-2019 Twitter, Inc.\n * Licensed under the Creative Commons Attribution 3.0 Unported License. For\n * details, see https://creativecommons.org/licenses/by/3.0/.\n */\n.hll {\n  background-color: #ffc;\n}\n.c {\n  color: #999;\n}\n.err {\n  color: #a00;\n  background-color: #faa;\n}\n.k {\n  color: #069;\n}\n.o {\n  color: #555;\n}\n.cm {\n  color: #999;\n}\n.cp {\n  color: #099;\n}\n.c1 {\n  color: #999;\n}\n.cs {\n  color: #999;\n}\n.gd {\n  background-color: #fcc;\n  border: 1px solid #c00;\n}\n.ge {\n  font-style: italic;\n}\n.gr {\n  color: #f00;\n}\n.gh {\n  color: #030;\n}\n.gi {\n  background-color: #cfc;\n  border: 1px solid #0c0;\n}\n.go {\n  color: #aaa;\n}\n.gp {\n  color: #009;\n}\n.gu {\n  color: #030;\n}\n.gt {\n  color: #9c6;\n}\n.kc {\n  color: #069;\n}\n.kd {\n  color: #069;\n}\n.kn {\n  color: #069;\n}\n.kp {\n  color: #069;\n}\n.kr {\n  color: #069;\n}\n.kt {\n  color: #078;\n}\n.m {\n  color: #f60;\n}\n.s {\n  color: #d44950;\n}\n.na {\n  color: #4f9fcf;\n}\n.nb {\n  color: #366;\n}\n.nc {\n  color: #0a8;\n}\n.no {\n  color: #360;\n}\n.nd {\n  color: #99f;\n}\n.ni {\n  color: #999;\n}\n.ne {\n  color: #c00;\n}\n.nf {\n  color: #c0f;\n}\n.nl {\n  color: #99f;\n}\n.nn {\n  color: #0cf;\n}\n.nt {\n  color: #2f6f9f;\n}\n.nv {\n  color: #033;\n}\n.ow {\n  color: #000;\n}\n.w {\n  color: #bbb;\n}\n.mf {\n  color: #f60;\n}\n.mh {\n  color: #f60;\n}\n.mi {\n  color: #f60;\n}\n.mo {\n  color: #f60;\n}\n.sb {\n  color: #c30;\n}\n.sc {\n  color: #c30;\n}\n.sd {\n  font-style: italic;\n  color: #c30;\n}\n.s2 {\n  color: #c30;\n}\n.se {\n  color: #c30;\n}\n.sh {\n  color: #c30;\n}\n.si {\n  color: #a00;\n}\n.sx {\n  color: #c30;\n}\n.sr {\n  color: #3aa;\n}\n.s1 {\n  color: #c30;\n}\n.ss {\n  color: #fc3;\n}\n.bp {\n  color: #366;\n}\n.vc {\n  color: #033;\n}\n.vg {\n  color: #033;\n}\n.vi {\n  color: #033;\n}\n.il {\n  color: #f60;\n}\n.css .o,\n.css .o + .nt,\n.css .nt + .nt {\n  color: #999;\n}\n.highlight {\n  padding: 9px 14px;\n  margin-bottom: 14px;\n  background-color: #f7f7f9;\n  border: 1px solid #e1e1e8;\n  border-radius: 4px;\n}\n.highlight pre {\n  padding: 0;\n  margin-top: 0;\n  margin-bottom: 0;\n  word-break: normal;\n  white-space: nowrap;\n  background-color: transparent;\n  border: 0;\n}\n.highlight pre code {\n  font-size: inherit;\n  color: #333;\n}\n.highlight pre code:first-child {\n  display: inline-block;\n  padding-right: 45px;\n}\n.language-bash:before {\n  color: #033;\n  content: \"$ \";\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n}\n/*!\n * IE10 viewport hack for Surface/desktop Windows 8 bug\n * Copyright 2014-2019 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n */\n@-ms-viewport {\n  width: device-width;\n}\n@viewport {\n  width: device-width;\n}\n.btn-outline {\n  color: #563d7c;\n  background-color: transparent;\n  border-color: #563d7c;\n}\n.btn-outline:hover,\n.btn-outline:focus,\n.btn-outline:active {\n  color: #fff;\n  background-color: #563d7c;\n  border-color: #563d7c;\n}\n.btn-outline-inverse {\n  color: #fff;\n  background-color: transparent;\n  border-color: #cdbfe3;\n}\n.btn-outline-inverse:hover,\n.btn-outline-inverse:focus,\n.btn-outline-inverse:active {\n  color: #563d7c;\n  text-shadow: none;\n  background-color: #fff;\n  border-color: #fff;\n}\n.bs-docs-booticon {\n  display: block;\n  font-weight: 500;\n  color: #fff;\n  text-align: center;\n  cursor: default;\n  background-color: #563d7c;\n  border-radius: 15%;\n}\n.bs-docs-booticon-sm {\n  width: 30px;\n  height: 30px;\n  font-size: 20px;\n  line-height: 28px;\n}\n.bs-docs-booticon-lg {\n  width: 144px;\n  height: 144px;\n  font-size: 108px;\n  line-height: 140px;\n}\n.bs-docs-booticon-inverse {\n  color: #563d7c;\n  background-color: #fff;\n}\n.bs-docs-booticon-outline {\n  background-color: transparent;\n  border: 1px solid #cdbfe3;\n}\n#skippy {\n  display: block;\n  padding: 1em;\n  color: #fff;\n  background-color: #6f5499;\n  outline: 0;\n}\n#skippy .skiplink-text {\n  padding: 0.5em;\n  outline: 1px dotted;\n}\n#content:focus {\n  outline: none;\n}\n.bs-docs-nav {\n  margin-bottom: 0;\n  background-color: #fff;\n  border-bottom: 0;\n}\n.bs-docs-nav .bs-nav-b {\n  display: none;\n}\n.bs-docs-nav .navbar-brand,\n.bs-docs-nav .navbar-nav > li > a {\n  font-weight: 500;\n  color: #563d7c;\n}\n.bs-docs-nav .navbar-nav > li > a {\n  padding-right: 10px;\n  padding-left: 10px;\n}\n.bs-docs-nav .navbar-nav > li > a:hover,\n.bs-docs-nav .navbar-nav > .active > a,\n.bs-docs-nav .navbar-nav > .active > a:hover {\n  color: #463265;\n  background-color: #f9f9f9;\n}\n.bs-docs-nav .navbar-toggle .icon-bar {\n  background-color: #563d7c;\n}\n.bs-docs-nav .navbar-header .navbar-toggle {\n  border-color: #fff;\n}\n.bs-docs-nav .navbar-header .navbar-toggle:hover,\n.bs-docs-nav .navbar-header .navbar-toggle:focus {\n  background-color: #f9f9f9;\n  border-color: #f9f9f9;\n}\n@media (min-width: 768px) and (max-width: 992px) {\n  .bs-docs-nav .navbar-right {\n    display: none;\n  }\n}\n.bs-docs-footer {\n  padding-top: 50px;\n  padding-bottom: 50px;\n  margin-top: 100px;\n  color: #99979c;\n  text-align: center;\n  background-color: #2a2730;\n}\n.bs-docs-footer a {\n  color: #fff;\n}\n.bs-docs-footer-links {\n  padding-left: 0;\n  margin-bottom: 20px;\n}\n.bs-docs-footer-links li {\n  display: inline-block;\n}\n.bs-docs-footer-links li + li {\n  margin-left: 15px;\n}\n@media (min-width: 768px) {\n  .bs-docs-footer {\n    text-align: left;\n  }\n  .bs-docs-footer p {\n    margin-bottom: 0;\n  }\n}\n.bs-docs-masthead,\n.bs-docs-header {\n  position: relative;\n  padding: 30px 0;\n  color: #cdbfe3;\n  text-align: center;\n  text-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);\n  background-color: #6f5499;\n  background-image: -webkit-gradient(linear, left top, left bottom, from(#563d7c), to(#6f5499));\n  background-image: -webkit-linear-gradient(top, #563d7c 0%, #6f5499 100%);\n  background-image: -o-linear-gradient(top, #563d7c 0%, #6f5499 100%);\n  background-image: linear-gradient(to bottom, #563d7c 0%, #6f5499 100%);\n  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=\"#563d7c\", endColorstr=\"#6F5499\", GradientType=0);\n  background-repeat: repeat-x;\n}\n.bs-docs-masthead .bs-docs-booticon {\n  margin: 0 auto 30px;\n}\n.bs-docs-masthead h1 {\n  font-weight: 300;\n  line-height: 1;\n  color: #fff;\n}\n.bs-docs-masthead .lead {\n  margin: 0 auto 30px;\n  font-size: 20px;\n  color: #fff;\n}\n.bs-docs-masthead .version {\n  margin-top: -15px;\n  margin-bottom: 30px;\n  color: #9783b9;\n}\n.bs-docs-masthead .btn {\n  width: 100%;\n  padding: 15px 30px;\n  font-size: 20px;\n}\n@media (min-width: 480px) {\n  .bs-docs-masthead .btn {\n    width: auto;\n  }\n}\n@media (min-width: 768px) {\n  .bs-docs-masthead {\n    padding: 80px 0;\n  }\n  .bs-docs-masthead h1 {\n    font-size: 60px;\n  }\n  .bs-docs-masthead .lead {\n    font-size: 24px;\n  }\n}\n@media (min-width: 992px) {\n  .bs-docs-masthead .lead {\n    width: 80%;\n    font-size: 30px;\n  }\n}\n.bs-docs-header {\n  margin-bottom: 40px;\n  font-size: 20px;\n}\n.bs-docs-header h1 {\n  margin-top: 0;\n  color: #fff;\n}\n.bs-docs-header p {\n  margin-bottom: 0;\n  font-weight: 300;\n  line-height: 1.4;\n}\n.bs-docs-header .container {\n  position: relative;\n}\n@media (min-width: 768px) {\n  .bs-docs-header {\n    padding-top: 60px;\n    padding-bottom: 60px;\n    font-size: 24px;\n    text-align: left;\n  }\n  .bs-docs-header h1 {\n    font-size: 60px;\n    line-height: 1;\n  }\n}\n@media (min-width: 992px) {\n  .bs-docs-header h1,\n  .bs-docs-header p {\n    margin-right: 380px;\n  }\n}\n#carbonads {\n  display: block;\n  padding: 15px 15px 15px 160px;\n  margin: 50px -15px -30px;\n  overflow: hidden;\n  font-size: 13px;\n  line-height: 1.5;\n  text-align: left;\n  border: solid #866ab3;\n  border-width: 1px 0 0;\n}\n#carbonads a {\n  color: #fff;\n  text-decoration: none;\n}\n@media (min-width: 768px) {\n  #carbonads {\n    max-width: 330px;\n    margin: 50px auto 0;\n    border-width: 1px;\n    border-radius: 4px;\n  }\n}\n@media (min-width: 992px) {\n  #carbonads {\n    position: absolute;\n    top: 0;\n    right: 15px;\n    margin-top: 0;\n  }\n  .bs-docs-masthead #carbonads {\n    position: static;\n  }\n}\n.carbon-img {\n  float: left;\n  margin-left: -145px;\n}\n.carbon-poweredby {\n  display: block;\n  color: #cdbfe3 !important;\n}\n.bs-docs-featurette {\n  padding-top: 40px;\n  padding-bottom: 40px;\n  font-size: 16px;\n  line-height: 1.5;\n  color: #555;\n  text-align: center;\n  background-color: #fff;\n  border-bottom: 1px solid #e5e5e5;\n}\n.bs-docs-featurette + .bs-docs-footer {\n  margin-top: 0;\n  border-top: 0;\n}\n.bs-docs-featurette-title {\n  margin-bottom: 5px;\n  font-size: 30px;\n  font-weight: 400;\n  color: #333;\n}\n.half-rule {\n  width: 100px;\n  margin: 40px auto;\n}\n.bs-docs-featurette h3 {\n  margin-bottom: 5px;\n  font-weight: 400;\n  color: #333;\n}\n.bs-docs-featurette-img {\n  display: block;\n  margin-bottom: 20px;\n  color: #333;\n}\n.bs-docs-featurette-img:hover {\n  color: #337ab7;\n  text-decoration: none;\n}\n.bs-docs-featurette-img img {\n  display: block;\n  margin-bottom: 15px;\n}\n@media (min-width: 480px) {\n  .bs-docs-featurette .img-responsive {\n    margin-top: 30px;\n  }\n}\n@media (min-width: 768px) {\n  .bs-docs-featurette {\n    padding-top: 100px;\n    padding-bottom: 100px;\n  }\n  .bs-docs-featurette-title {\n    font-size: 40px;\n  }\n  .bs-docs-featurette .lead {\n    max-width: 80%;\n    margin-right: auto;\n    margin-left: auto;\n  }\n  .bs-docs-featurette .img-responsive {\n    margin-top: 0;\n  }\n}\n.bs-docs-featured-sites {\n  margin-right: -1px;\n  margin-left: -1px;\n}\n.bs-docs-featured-sites .col-xs-8 {\n  padding: 1px;\n}\n.bs-docs-featured-sites .img-responsive {\n  margin-top: 0;\n}\n@media (min-width: 768px) {\n  .bs-docs-featured-sites .col-sm-4:first-child img {\n    border-top-left-radius: 4px;\n    border-bottom-left-radius: 4px;\n  }\n  .bs-docs-featured-sites .col-sm-4:last-child img {\n    border-top-right-radius: 4px;\n    border-bottom-right-radius: 4px;\n  }\n}\n.bs-examples .thumbnail {\n  margin-bottom: 10px;\n}\n.bs-examples h4 {\n  margin-bottom: 5px;\n}\n.bs-examples p {\n  margin-bottom: 20px;\n}\n@media (max-width: 480px) {\n  .bs-examples {\n    margin-right: -10px;\n    margin-left: -10px;\n  }\n  .bs-examples > [class^=\"col-\"] {\n    padding-right: 10px;\n    padding-left: 10px;\n  }\n}\n.bs-docs-sidebar.affix {\n  position: static;\n}\n@media (min-width: 768px) {\n  .bs-docs-sidebar {\n    padding-left: 20px;\n  }\n}\n.bs-docs-search {\n  margin-bottom: 20px;\n  margin-left: 20px;\n}\n.bs-docs-sidenav {\n  margin-top: 20px;\n  margin-bottom: 20px;\n}\n.bs-docs-sidebar .nav > li > a {\n  display: block;\n  padding: 4px 20px;\n  font-size: 13px;\n  font-weight: 500;\n  color: #767676;\n}\n.bs-docs-sidebar .nav > li > a:hover,\n.bs-docs-sidebar .nav > li > a:focus {\n  padding-left: 19px;\n  color: #563d7c;\n  text-decoration: none;\n  background-color: transparent;\n  border-left: 1px solid #563d7c;\n}\n.bs-docs-sidebar .nav > .active > a,\n.bs-docs-sidebar .nav > .active:hover > a,\n.bs-docs-sidebar .nav > .active:focus > a {\n  padding-left: 18px;\n  font-weight: 700;\n  color: #563d7c;\n  background-color: transparent;\n  border-left: 2px solid #563d7c;\n}\n.bs-docs-sidebar .nav .nav {\n  display: none;\n  padding-bottom: 10px;\n}\n.bs-docs-sidebar .nav .nav > li > a {\n  padding-top: 1px;\n  padding-bottom: 1px;\n  padding-left: 30px;\n  font-size: 12px;\n  font-weight: 400;\n}\n.bs-docs-sidebar .nav .nav > li > a:hover,\n.bs-docs-sidebar .nav .nav > li > a:focus {\n  padding-left: 29px;\n}\n.bs-docs-sidebar .nav .nav > .active > a,\n.bs-docs-sidebar .nav .nav > .active:hover > a,\n.bs-docs-sidebar .nav .nav > .active:focus > a {\n  padding-left: 28px;\n  font-weight: 500;\n}\n.back-to-top,\n.bs-docs-theme-toggle {\n  display: none;\n  padding: 4px 10px;\n  margin-top: 10px;\n  margin-left: 10px;\n  font-size: 12px;\n  font-weight: 500;\n  color: #999;\n}\n.back-to-top:hover,\n.bs-docs-theme-toggle:hover {\n  color: #563d7c;\n  text-decoration: none;\n}\n.bs-docs-theme-toggle {\n  margin-top: 0;\n}\n@media (min-width: 768px) {\n  .back-to-top,\n  .bs-docs-theme-toggle {\n    display: block;\n  }\n}\n@media (min-width: 992px) {\n  .bs-docs-sidebar .nav > .active > ul {\n    display: block;\n  }\n  .bs-docs-sidebar.affix,\n  .bs-docs-sidebar.affix-bottom {\n    width: 213px;\n  }\n  .bs-docs-sidebar.affix {\n    position: fixed;\n    top: 20px;\n  }\n  .bs-docs-sidebar.affix-bottom {\n    position: absolute;\n  }\n  .bs-docs-sidebar.affix-bottom .bs-docs-sidenav,\n  .bs-docs-sidebar.affix .bs-docs-sidenav {\n    margin-top: 0;\n    margin-bottom: 0;\n  }\n}\n@media (min-width: 1200px) {\n  .bs-docs-sidebar.affix-bottom,\n  .bs-docs-sidebar.affix {\n    width: 263px;\n  }\n}\n.show-grid {\n  margin-bottom: 15px;\n}\n.show-grid [class^=\"col-\"] {\n  padding-top: 10px;\n  padding-bottom: 10px;\n  background-color: #eee;\n  background-color: rgba(86, 61, 124, 0.15);\n  border: 1px solid #ddd;\n  border: 1px solid rgba(86, 61, 124, 0.2);\n}\n.bs-example {\n  position: relative;\n  padding: 45px 15px 15px;\n  margin: 0 -15px 15px;\n  border-color: #e5e5e5 #eee #eee;\n  border-style: solid;\n  border-width: 1px 0;\n  -webkit-box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.05);\n  box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.05);\n}\n.bs-example:after {\n  position: absolute;\n  top: 15px;\n  left: 15px;\n  font-size: 12px;\n  font-weight: 700;\n  color: #959595;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n  content: \"Example\";\n}\n.bs-example-padded-bottom {\n  padding-bottom: 24px;\n}\n.bs-example + .highlight,\n.bs-example + .bs-clipboard + .highlight {\n  margin: -15px -15px 15px;\n  border-width: 0 0 1px;\n  border-radius: 0;\n}\n@media (min-width: 768px) {\n  .bs-example {\n    margin-right: 0;\n    margin-left: 0;\n    background-color: #fff;\n    border-color: #ddd;\n    border-width: 1px;\n    border-radius: 4px 4px 0 0;\n    -webkit-box-shadow: none;\n    box-shadow: none;\n  }\n  .bs-example + .highlight,\n  .bs-example + .bs-clipboard + .highlight {\n    margin-top: -16px;\n    margin-right: 0;\n    margin-left: 0;\n    border-width: 1px;\n    border-bottom-right-radius: 4px;\n    border-bottom-left-radius: 4px;\n  }\n  .bs-example + .bs-clipboard .btn-clipboard {\n    top: -15px;\n    border-top-right-radius: 0;\n  }\n  .bs-example-standalone {\n    border-radius: 4px;\n  }\n}\n.bs-example .container {\n  width: auto;\n}\n.bs-example > p:last-child,\n.bs-example > ul:last-child,\n.bs-example > ol:last-child,\n.bs-example > blockquote:last-child,\n.bs-example > .form-control:last-child,\n.bs-example > .table:last-child,\n.bs-example > .navbar:last-child,\n.bs-example > .jumbotron:last-child,\n.bs-example > .alert:last-child,\n.bs-example > .panel:last-child,\n.bs-example > .list-group:last-child,\n.bs-example > .well:last-child,\n.bs-example > .progress:last-child,\n.bs-example > .table-responsive:last-child > .table {\n  margin-bottom: 0;\n}\n.bs-example > p > .close {\n  float: none;\n}\n.bs-example-type .table .type-info {\n  color: #767676;\n  vertical-align: middle;\n}\n.bs-example-type .table td {\n  padding: 15px 0;\n  border-color: #eee;\n}\n.bs-example-type .table tr:first-child td {\n  border-top: 0;\n}\n.bs-example-type h1,\n.bs-example-type h2,\n.bs-example-type h3,\n.bs-example-type h4,\n.bs-example-type h5,\n.bs-example-type h6 {\n  margin: 0;\n}\n.bs-example-bg-classes p {\n  padding: 15px;\n}\n.bs-example > .img-circle,\n.bs-example > .img-rounded,\n.bs-example > .img-thumbnail {\n  margin: 5px;\n}\n.bs-example > .table-responsive > .table {\n  background-color: #fff;\n}\n.bs-example > .btn,\n.bs-example > .btn-group {\n  margin-top: 5px;\n  margin-bottom: 5px;\n}\n.bs-example > .btn-toolbar + .btn-toolbar {\n  margin-top: 10px;\n}\n.bs-example-control-sizing select,\n.bs-example-control-sizing input[type=\"text\"] + input[type=\"text\"] {\n  margin-top: 10px;\n}\n.bs-example-form .input-group {\n  margin-bottom: 10px;\n}\n.bs-example > textarea.form-control {\n  resize: vertical;\n}\n.bs-example > .list-group {\n  max-width: 400px;\n}\n.bs-example .navbar:last-child {\n  margin-bottom: 0;\n}\n.bs-navbar-top-example,\n.bs-navbar-bottom-example {\n  z-index: 1;\n  padding: 0;\n  overflow: hidden;\n}\n.bs-navbar-top-example .navbar-header,\n.bs-navbar-bottom-example .navbar-header {\n  margin-left: 0;\n}\n.bs-navbar-top-example .navbar-fixed-top,\n.bs-navbar-bottom-example .navbar-fixed-bottom {\n  position: relative;\n  margin-right: 0;\n  margin-left: 0;\n}\n.bs-navbar-top-example {\n  padding-bottom: 45px;\n}\n.bs-navbar-top-example:after {\n  top: auto;\n  bottom: 15px;\n}\n.bs-navbar-top-example .navbar-fixed-top {\n  top: -1px;\n}\n.bs-navbar-bottom-example {\n  padding-top: 45px;\n}\n.bs-navbar-bottom-example .navbar-fixed-bottom {\n  bottom: -1px;\n}\n.bs-navbar-bottom-example .navbar {\n  margin-bottom: 0;\n}\n@media (min-width: 768px) {\n  .bs-navbar-top-example .navbar-fixed-top,\n  .bs-navbar-bottom-example .navbar-fixed-bottom {\n    position: absolute;\n  }\n}\n.bs-example .pagination {\n  margin-top: 10px;\n  margin-bottom: 10px;\n}\n.bs-example > .pager {\n  margin-top: 0;\n}\n.bs-example-modal {\n  background-color: #f5f5f5;\n}\n.bs-example-modal .modal {\n  position: relative;\n  top: auto;\n  right: auto;\n  bottom: auto;\n  left: auto;\n  z-index: 1;\n  display: block;\n}\n.bs-example-modal .modal-dialog {\n  left: auto;\n  margin-right: auto;\n  margin-left: auto;\n}\n.bs-example > .dropdown > .dropdown-toggle {\n  float: left;\n}\n.bs-example > .dropdown > .dropdown-menu {\n  position: static;\n  display: block;\n  margin-bottom: 5px;\n  clear: left;\n}\n.bs-example-tabs .nav-tabs {\n  margin-bottom: 15px;\n}\n.bs-example-tooltips {\n  text-align: center;\n}\n.bs-example-tooltips > .btn {\n  margin-top: 5px;\n  margin-bottom: 5px;\n}\n.bs-example-tooltip .tooltip {\n  position: relative;\n  display: inline-block;\n  margin: 10px 20px;\n  opacity: 1;\n}\n.bs-example-popover {\n  padding-bottom: 24px;\n  background-color: #f9f9f9;\n}\n.bs-example-popover .popover {\n  position: relative;\n  display: block;\n  float: left;\n  width: 260px;\n  margin: 20px;\n}\n.scrollspy-example {\n  position: relative;\n  height: 200px;\n  margin-top: 10px;\n  overflow: auto;\n}\n.bs-example > .nav-pills-stacked-example {\n  max-width: 300px;\n}\n#collapseExample .well {\n  margin-bottom: 0;\n}\n#focusedInput {\n  border-color: #cccccc;\n  border-color: rgba(82, 168, 236, 0.8);\n  outline: 0;\n  outline: thin dotted \\9;\n  -webkit-box-shadow: 0 0 8px rgba(82, 168, 236, 0.6);\n  box-shadow: 0 0 8px rgba(82, 168, 236, 0.6);\n}\n.bs-callout {\n  padding: 20px;\n  margin: 20px 0;\n  border: 1px solid #eee;\n  border-left-width: 5px;\n  border-radius: 3px;\n}\n.bs-callout h4 {\n  margin-top: 0;\n  margin-bottom: 5px;\n}\n.bs-callout p:last-child {\n  margin-bottom: 0;\n}\n.bs-callout code {\n  border-radius: 3px;\n}\n.bs-callout + .bs-callout {\n  margin-top: -5px;\n}\n.bs-callout-danger {\n  border-left-color: #ce4844;\n}\n.bs-callout-danger h4 {\n  color: #ce4844;\n}\n.bs-callout-warning {\n  border-left-color: #aa6708;\n}\n.bs-callout-warning h4 {\n  color: #aa6708;\n}\n.bs-callout-info {\n  border-left-color: #1b809e;\n}\n.bs-callout-info h4 {\n  color: #1b809e;\n}\n.color-swatches {\n  margin: 0 -5px;\n  overflow: hidden;\n  /* clearfix */\n}\n.color-swatch {\n  float: left;\n  width: 60px;\n  height: 60px;\n  margin: 0 5px;\n  border-radius: 3px;\n}\n@media (min-width: 768px) {\n  .color-swatch {\n    width: 100px;\n    height: 100px;\n  }\n}\n.color-swatches .gray-darker {\n  background-color: #222;\n}\n.color-swatches .gray-dark {\n  background-color: #333;\n}\n.color-swatches .gray {\n  background-color: #555;\n}\n.color-swatches .gray-light {\n  background-color: #999;\n}\n.color-swatches .gray-lighter {\n  background-color: #eee;\n}\n.color-swatches .brand-primary {\n  background-color: #337ab7;\n}\n.color-swatches .brand-success {\n  background-color: #5cb85c;\n}\n.color-swatches .brand-warning {\n  background-color: #f0ad4e;\n}\n.color-swatches .brand-danger {\n  background-color: #d9534f;\n}\n.color-swatches .brand-info {\n  background-color: #5bc0de;\n}\n.color-swatches .bs-purple {\n  background-color: #563d7c;\n}\n.color-swatches .bs-purple-light {\n  background-color: #c7bfd3;\n}\n.color-swatches .bs-purple-lighter {\n  background-color: #e5e1ea;\n}\n.color-swatches .bs-gray {\n  background-color: #f9f9f9;\n}\n.bs-team .team-member {\n  line-height: 32px;\n  color: #555;\n}\n.bs-team .team-member:hover {\n  color: #333;\n  text-decoration: none;\n}\n.bs-team .github-btn {\n  float: right;\n  width: 180px;\n  height: 20px;\n  margin-top: 6px;\n  border: none;\n}\n.bs-team img {\n  float: left;\n  width: 32px;\n  margin-right: 10px;\n  border-radius: 4px;\n}\n.table-responsive .highlight pre {\n  white-space: normal;\n}\n.bs-table th small,\n.responsive-utilities th small {\n  display: block;\n  font-weight: 400;\n  color: #999;\n}\n.responsive-utilities tbody th {\n  font-weight: 400;\n}\n.responsive-utilities td {\n  text-align: center;\n}\n.responsive-utilities td.is-visible {\n  color: #468847;\n  background-color: #dff0d8 !important;\n}\n.responsive-utilities td.is-hidden {\n  color: #ccc;\n  background-color: #f9f9f9 !important;\n}\n.responsive-utilities-test {\n  margin-top: 5px;\n}\n.responsive-utilities-test .col-xs-6 {\n  margin-bottom: 10px;\n}\n.responsive-utilities-test span {\n  display: block;\n  padding: 15px 10px;\n  font-size: 14px;\n  font-weight: 700;\n  line-height: 1.1;\n  text-align: center;\n  border-radius: 4px;\n}\n.visible-on .col-xs-6 .hidden-xs,\n.visible-on .col-xs-6 .hidden-sm,\n.visible-on .col-xs-6 .hidden-md,\n.visible-on .col-xs-6 .hidden-lg,\n.hidden-on .col-xs-6 .hidden-xs,\n.hidden-on .col-xs-6 .hidden-sm,\n.hidden-on .col-xs-6 .hidden-md,\n.hidden-on .col-xs-6 .hidden-lg {\n  color: #999;\n  border: 1px solid #ddd;\n}\n.visible-on .col-xs-6 .visible-xs-block,\n.visible-on .col-xs-6 .visible-sm-block,\n.visible-on .col-xs-6 .visible-md-block,\n.visible-on .col-xs-6 .visible-lg-block,\n.hidden-on .col-xs-6 .visible-xs-block,\n.hidden-on .col-xs-6 .visible-sm-block,\n.hidden-on .col-xs-6 .visible-md-block,\n.hidden-on .col-xs-6 .visible-lg-block {\n  color: #468847;\n  background-color: #dff0d8;\n  border: 1px solid #d6e9c6;\n}\n.bs-glyphicons {\n  margin: 0 -10px 20px;\n  overflow: hidden;\n}\n.bs-glyphicons-list {\n  padding-left: 0;\n  list-style: none;\n}\n.bs-glyphicons li {\n  float: left;\n  width: 25%;\n  height: 115px;\n  padding: 10px;\n  font-size: 10px;\n  line-height: 1.4;\n  text-align: center;\n  background-color: #f9f9f9;\n  border: 1px solid #fff;\n}\n.bs-glyphicons .glyphicon {\n  margin-top: 5px;\n  margin-bottom: 10px;\n  font-size: 24px;\n}\n.bs-glyphicons .glyphicon-class {\n  display: block;\n  text-align: center;\n  word-wrap: break-word;\n}\n.bs-glyphicons li:hover {\n  color: #fff;\n  background-color: #563d7c;\n}\n@media (min-width: 768px) {\n  .bs-glyphicons {\n    margin-right: 0;\n    margin-left: 0;\n  }\n  .bs-glyphicons li {\n    width: 12.5%;\n    font-size: 12px;\n  }\n}\n.bs-customizer .toggle {\n  float: right;\n  margin-top: 25px;\n}\n.bs-customizer label {\n  margin-top: 10px;\n  font-weight: 500;\n  color: #555;\n}\n.bs-customizer h2 {\n  padding-top: 30px;\n  margin-top: 0;\n  margin-bottom: 5px;\n}\n.bs-customizer h3 {\n  margin-bottom: 0;\n}\n.bs-customizer h4 {\n  margin-top: 15px;\n  margin-bottom: 0;\n}\n.bs-customizer .bs-callout h4 {\n  margin-top: 0;\n  margin-bottom: 5px;\n}\n.bs-customizer input[type=\"text\"] {\n  font-family: Menlo, Monaco, Consolas, \"Courier New\", monospace;\n  background-color: #fafafa;\n}\n.bs-customizer .help-block {\n  margin-bottom: 5px;\n  font-size: 12px;\n}\n#less-section label {\n  font-weight: 400;\n}\n.bs-customize-download .btn-outline {\n  padding: 20px;\n}\n.bs-customizer-alert {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 1030;\n  padding: 15px 0;\n  color: #fff;\n  background-color: #d9534f;\n  border-bottom: 1px solid #b94441;\n  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.25);\n  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.25);\n}\n.bs-customizer-alert .close {\n  margin-top: -4px;\n  font-size: 24px;\n}\n.bs-customizer-alert p {\n  margin-bottom: 0;\n}\n.bs-customizer-alert .glyphicon {\n  margin-right: 5px;\n}\n.bs-customizer-alert pre {\n  margin: 10px 0 0;\n  color: #fff;\n  background-color: #a83c3a;\n  border-color: #973634;\n  -webkit-box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05), 0 1px 0 rgba(255, 255, 255, 0.1);\n  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05), 0 1px 0 rgba(255, 255, 255, 0.1);\n}\n.bs-dropzone {\n  position: relative;\n  padding: 20px;\n  margin-bottom: 20px;\n  color: #777;\n  text-align: center;\n  border: 2px dashed #eee;\n  border-radius: 4px;\n}\n.bs-dropzone .import-header {\n  margin-bottom: 5px;\n}\n.bs-dropzone .glyphicon-folder-open {\n  font-size: 40px;\n}\n.bs-dropzone hr {\n  width: 100px;\n}\n.bs-dropzone .lead {\n  margin-bottom: 10px;\n  font-weight: 400;\n  color: #333;\n}\n#import-manual-trigger {\n  cursor: pointer;\n}\n.bs-dropzone p:last-child {\n  margin-bottom: 0;\n}\n.bs-brand-logos {\n  display: table;\n  width: 100%;\n  margin-bottom: 15px;\n  overflow: hidden;\n  color: #563d7c;\n  background-color: #f9f9f9;\n  border-radius: 4px;\n}\n.bs-brand-item {\n  padding: 60px 0;\n  text-align: center;\n}\n.bs-brand-item + .bs-brand-item {\n  border-top: 1px solid #fff;\n}\n.bs-brand-logos .inverse {\n  color: #fff;\n  background-color: #563d7c;\n}\n.bs-brand-item h1,\n.bs-brand-item h3 {\n  margin-top: 0;\n  margin-bottom: 0;\n}\n.bs-brand-item .bs-docs-booticon {\n  margin-right: auto;\n  margin-left: auto;\n}\n.bs-brand-item .glyphicon {\n  width: 30px;\n  height: 30px;\n  margin: 10px auto -10px;\n  line-height: 30px;\n  color: #fff;\n  border-radius: 50%;\n}\n.bs-brand-item .glyphicon-ok {\n  background-color: #5cb85c;\n}\n.bs-brand-item .glyphicon-remove {\n  background-color: #d9534f;\n}\n@media (min-width: 768px) {\n  .bs-brand-item {\n    display: table-cell;\n    width: 1%;\n  }\n  .bs-brand-item + .bs-brand-item {\n    border-top: 0;\n    border-left: 1px solid #fff;\n  }\n  .bs-brand-item h1 {\n    font-size: 60px;\n  }\n}\n.bs-clipboard {\n  position: relative;\n  display: none;\n  float: right;\n}\n.bs-clipboard + .highlight {\n  margin-top: 0;\n}\n.btn-clipboard {\n  position: absolute;\n  top: 0;\n  right: 0;\n  z-index: 10;\n  display: block;\n  padding: 4px 8px;\n  font-size: 12px;\n  color: #818a91;\n  cursor: pointer;\n  background-color: transparent;\n  border: 0;\n  border-top-right-radius: 4px;\n  border-bottom-left-radius: 4px;\n}\n.btn-clipboard:hover {\n  color: #fff;\n  background-color: #027de7;\n}\n@media (min-width: 768px) {\n  .bs-clipboard {\n    display: block;\n  }\n}\n.anchorjs-link {\n  color: inherit;\n}\n@media (max-width: 480px) {\n  .anchorjs-link {\n    display: none;\n  }\n}\n*:hover > .anchorjs-link {\n  opacity: 0.75;\n  -webkit-transition: color 0.16s linear;\n  -o-transition: color 0.16s linear;\n  transition: color 0.16s linear;\n}\n*:hover > .anchorjs-link:hover,\n.anchorjs-link:focus {\n  text-decoration: none;\n  opacity: 1;\n}\n.algolia-autocomplete {\n  display: block !important;\n}\n.algolia-autocomplete .ds-dropdown-menu {\n  width: 100%;\n  min-width: 0 !important;\n  max-width: none !important;\n  padding: 10px 0 !important;\n  background-color: #fff;\n  background-clip: padding-box;\n  border: 1px solid #ddd;\n  border: 1px solid rgba(0, 0, 0, 0.1);\n  -webkit-box-shadow: 0 8px 15px rgba(0, 0, 0, 0.175);\n  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.175);\n}\n@media (min-width: 768px) {\n  .algolia-autocomplete .ds-dropdown-menu {\n    width: 175%;\n  }\n}\n.algolia-autocomplete .ds-dropdown-menu:before {\n  display: none !important;\n}\n.algolia-autocomplete .ds-dropdown-menu [class^=\"ds-dataset-\"] {\n  padding: 0 !important;\n  overflow: visible !important;\n  background-color: transparent !important;\n  border: 0 !important;\n}\n.algolia-autocomplete .ds-dropdown-menu .ds-suggestions {\n  margin-top: 0 !important;\n}\n.algolia-autocomplete .ds-dropdown-menu .ds-input {\n  -webkit-box-shadow: none;\n  box-shadow: none;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion {\n  padding: 0 !important;\n  overflow: visible !important;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion--category-header {\n  padding: 2px 15px !important;\n  margin-top: 0 !important;\n  font-size: 13px !important;\n  font-weight: 500 !important;\n  color: #7952b3 !important;\n  border-bottom: 0 !important;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion--wrapper {\n  float: none !important;\n  padding-top: 0 !important;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion--subcategory-column {\n  float: none !important;\n  width: auto !important;\n  padding: 0 !important;\n  text-align: left !important;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion--content {\n  float: none !important;\n  width: auto !important;\n  padding: 0 !important;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion--content:before {\n  display: none !important;\n}\n.algolia-autocomplete .ds-suggestion:not(:first-child) .algolia-docsearch-suggestion--category-header {\n  padding-top: 10px !important;\n  margin-top: 10px !important;\n  border-top: 1px solid #eee;\n}\n.algolia-autocomplete .ds-suggestion .algolia-docsearch-suggestion--subcategory-column {\n  display: none !important;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion--title {\n  display: block;\n  padding: 4px 15px !important;\n  margin-bottom: 0 !important;\n  font-size: 13px !important;\n  font-weight: 400 !important;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion--text {\n  padding: 0 15px 8px !important;\n  margin-top: -4px;\n  font-size: 13px !important;\n  font-weight: 400;\n  line-height: 1.25 !important;\n}\n.algolia-autocomplete .algolia-docsearch-footer {\n  float: none !important;\n  width: auto !important;\n  height: auto !important;\n  padding: 10px 15px 0;\n  font-size: 10px !important;\n  line-height: 1 !important;\n  color: #767676 !important;\n  border-top: 1px solid #eee;\n}\n.algolia-autocomplete .algolia-docsearch-footer--logo {\n  display: inline !important;\n  overflow: visible !important;\n  color: inherit !important;\n  text-indent: 0 !important;\n  background: none !important;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion--highlight {\n  color: #5f2dab;\n  background-color: #eee;\n}\n.algolia-autocomplete .algolia-docsearch-suggestion--text .algolia-docsearch-suggestion--highlight {\n  -webkit-box-shadow: inset 0 -2px 0 0 rgba(95, 45, 171, 0.5) !important;\n  box-shadow: inset 0 -2px 0 0 rgba(95, 45, 171, 0.5) !important;\n}\n.algolia-autocomplete .ds-suggestion.ds-cursor .algolia-docsearch-suggestion--content {\n  background-color: #e5e5e5 !important;\n}\nbody {\n  position: relative;\n}\n.table code {\n  font-size: 13px;\n  font-weight: 400;\n}\nh2 code,\nh3 code,\nh4 code {\n  background-color: inherit;\n}\n.bs-docs-section {\n  margin-bottom: 60px;\n}\n.bs-docs-section:last-child {\n  margin-bottom: 0;\n}\nh1[id] {\n  padding-top: 20px;\n  margin-top: 0;\n}\n.bs-docs-browser-bugs td p {\n  margin-bottom: 0;\n}\n.bs-docs-browser-bugs th:first-child {\n  width: 18%;\n}\n.bs-events-table > thead > tr > th:first-child,\n.bs-events-table > tbody > tr > td:first-child {\n  white-space: nowrap;\n}\n.bs-events-table > thead > tr > th:first-child {\n  width: 150px;\n}\n.js-options-table > thead > tr > th:nth-child(1),\n.js-options-table > thead > tr > th:nth-child(2) {\n  width: 100px;\n}\n.js-options-table > thead > tr > th:nth-child(3) {\n  width: 50px;\n}\n.v4-tease {\n  display: block;\n  padding: 15px 20px;\n  font-weight: 700;\n  color: #fff;\n  text-align: center;\n  background-color: #0275d8;\n}\n.v4-tease:focus,\n.v4-tease:hover {\n  color: #fff;\n  text-decoration: none;\n  background-color: #0269c2;\n}\n/* Nullify ill-advised printing of hrefs; see #18711 */\n@media print {\n  a[href]:after {\n    content: \"\" !important;\n  }\n}\n/*# sourceMappingURL=docs.css.map */", "// stylelint-disable at-rule-no-vendor-prefix\n\n/*!\n * IE10 viewport hack for Surface/desktop Windows 8 bug\n * Copyright 2014-2019 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n */\n\n// See the Getting Started docs for more information:\n// https://getbootstrap.com/docs/3.4/getting-started/#support-ie10-width\n\n@-ms-viewport     { width: device-width; }\n@-o-viewport      { width: device-width; }\n@viewport         { width: device-width; }\n", "// Outline button for use within the docs\n.btn-outline {\n  color: #563d7c;\n  background-color: transparent;\n  border-color: #563d7c;\n\n  &:hover,\n  &:focus,\n  &:active {\n    color: #fff;\n    background-color: #563d7c;\n    border-color: #563d7c;\n  }\n}\n\n// Inverted outline button (white on dark)\n.btn-outline-inverse {\n  color: #fff;\n  background-color: transparent;\n  border-color: #cdbfe3;\n\n  &:hover,\n  &:focus,\n  &:active {\n    color: #563d7c;\n    text-shadow: none;\n    background-color: #fff;\n    border-color: #fff;\n  }\n}\n", ".bs-docs-booticon {\n  display: block;\n  font-weight: 500;\n  color: #fff;\n  text-align: center;\n  cursor: default;\n  background-color: #563d7c;\n  border-radius: 15%;\n}\n\n.bs-docs-booticon-sm {\n  width: 30px;\n  height: 30px;\n  font-size: 20px;\n  line-height: 28px;\n}\n\n.bs-docs-booticon-lg {\n  width: 144px;\n  height: 144px;\n  font-size: 108px;\n  line-height: 140px;\n}\n\n.bs-docs-booticon-inverse {\n  color: #563d7c;\n  background-color: #fff;\n}\n\n.bs-docs-booticon-outline {\n  background-color: transparent;\n  border: 1px solid #cdbfe3;\n}\n", "// stylelint-disable selector-max-id\n\n#skippy {\n  display: block;\n  padding: 1em;\n  color: #fff;\n  background-color: #6f5499;\n  outline: 0;\n}\n\n#skippy .skiplink-text {\n  padding: .5em;\n  outline: 1px dotted;\n}\n\n#content:focus {\n  outline: none;\n}\n", ".bs-docs-nav {\n  margin-bottom: 0;\n  background-color: #fff;\n  border-bottom: 0;\n\n  .bs-nav-b {\n    display: none;\n  }\n\n  .navbar-brand,\n  .navbar-nav > li > a {\n    font-weight: 500;\n    color: #563d7c;\n  }\n\n  .navbar-nav {\n    > li > a {\n      padding-right: 10px;\n      padding-left: 10px;\n    }\n\n    > li > a:hover,\n    > .active > a,\n    > .active > a:hover {\n      color: #463265;\n      background-color: #f9f9f9;\n    }\n  }\n\n  .navbar-toggle .icon-bar {\n    background-color: #563d7c;\n  }\n\n  .navbar-header {\n    .navbar-toggle {\n      border-color: #fff;\n\n      &:hover,\n      &:focus {\n        background-color: #f9f9f9;\n        border-color: #f9f9f9;\n      }\n    }\n  }\n\n  .navbar-right {\n    @media (min-width: 768px) and (max-width: 992px) {\n      display: none;\n    }\n  }\n}\n", ".bs-docs-footer {\n  padding-top: 50px;\n  padding-bottom: 50px;\n  margin-top: 100px;\n  color: #99979c;\n  text-align: center;\n  background-color: #2a2730;\n}\n.bs-docs-footer a {\n  color: #fff;\n}\n.bs-docs-footer-links {\n  padding-left: 0;\n  margin-bottom: 20px;\n}\n.bs-docs-footer-links li {\n  display: inline-block;\n}\n.bs-docs-footer-links li + li {\n  margin-left: 15px;\n}\n\n@media (min-width: @screen-sm-min) {\n  .bs-docs-footer {\n    text-align: left;\n  }\n  .bs-docs-footer p {\n    margin-bottom: 0;\n  }\n}\n", "// stylelint-disable value-no-vendor-prefix, function-name-case\n\n.bs-docs-masthead,\n.bs-docs-header {\n  position: relative;\n  padding: 30px 0;\n  color: #cdbfe3;\n  text-align: center;\n  text-shadow: 0 1px 0 rgba(0, 0, 0, .1);\n  background-color: #6f5499;\n  background-image: -webkit-gradient(linear, left top, left bottom, from(#563d7c), to(#6f5499));\n  background-image: -webkit-linear-gradient(top, #563d7c 0%, #6f5499 100%);\n  background-image: -o-linear-gradient(top, #563d7c 0%, #6f5499 100%);\n  background-image: linear-gradient(to bottom, #563d7c 0%, #6f5499 100%);\n  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=\"#563d7c\", endColorstr=\"#6F5499\", GradientType=0);\n  background-repeat: repeat-x;\n}\n\n// Masthead (headings and download button)\n.bs-docs-masthead .bs-docs-booticon {\n  margin: 0 auto 30px;\n}\n.bs-docs-masthead h1 {\n  font-weight: 300;\n  line-height: 1;\n  color: #fff;\n}\n.bs-docs-masthead .lead {\n  margin: 0 auto 30px;\n  font-size: 20px;\n  color: #fff;\n}\n.bs-docs-masthead .version {\n  margin-top: -15px;\n  margin-bottom: 30px;\n  color: #9783b9;\n}\n.bs-docs-masthead .btn {\n  width: 100%;\n  padding: 15px 30px;\n  font-size: 20px;\n}\n\n@media (min-width: @screen-xs-min) {\n  .bs-docs-masthead .btn {\n    width: auto;\n  }\n}\n\n@media (min-width: @screen-sm-min) {\n  .bs-docs-masthead {\n    padding: 80px 0;\n  }\n  .bs-docs-masthead h1 {\n    font-size: 60px;\n  }\n  .bs-docs-masthead .lead {\n    font-size: 24px;\n  }\n}\n\n@media (min-width: @screen-md-min) {\n  .bs-docs-masthead .lead {\n    width: 80%;\n    font-size: 30px;\n  }\n}\n", ".bs-docs-header {\n  margin-bottom: 40px;\n  font-size: 20px;\n}\n.bs-docs-header h1 {\n  margin-top: 0;\n  color: #fff;\n}\n.bs-docs-header p {\n  margin-bottom: 0;\n  font-weight: 300;\n  line-height: 1.4;\n}\n.bs-docs-header .container {\n  position: relative;\n}\n\n@media (min-width: @screen-sm-min) {\n  .bs-docs-header {\n    padding-top: 60px;\n    padding-bottom: 60px;\n    font-size: 24px;\n    text-align: left;\n  }\n  .bs-docs-header h1 {\n    font-size: 60px;\n    line-height: 1;\n  }\n}\n\n@media (min-width: @screen-md-min) {\n  .bs-docs-header h1,\n  .bs-docs-header p {\n    margin-right: 380px;\n  }\n}\n", "// stylelint-disable selector-max-id, declaration-no-important\n\n#carbonads {\n  display: block;\n  padding: 15px 15px 15px 160px;\n  margin: 50px -15px -30px;\n  overflow: hidden;\n  font-size: 13px;\n  line-height: 1.5;\n  text-align: left;\n  border: solid #866ab3;\n  border-width: 1px 0 0;\n\n  a {\n    color: #fff;\n    text-decoration: none;\n  }\n\n  @media (min-width: @screen-sm-min) {\n    max-width: 330px;\n    margin: 50px auto 0;\n    border-width: 1px;\n    border-radius: 4px;\n  }\n\n  @media (min-width: @screen-md-min) {\n    position: absolute;\n    top: 0;\n    right: 15px;\n    margin-top: 0;\n\n    .bs-docs-masthead & {\n      position: static;\n    }\n  }\n}\n\n.carbon-img {\n  float: left;\n  margin-left: -145px;\n}\n\n.carbon-poweredby {\n  display: block;\n  color: #cdbfe3 !important;\n}\n", ".bs-docs-featurette {\n  padding-top: 40px;\n  padding-bottom: 40px;\n  font-size: 16px;\n  line-height: 1.5;\n  color: #555;\n  text-align: center;\n  background-color: #fff;\n  border-bottom: 1px solid #e5e5e5;\n}\n.bs-docs-featurette + .bs-docs-footer {\n  margin-top: 0;\n  border-top: 0;\n}\n\n.bs-docs-featurette-title {\n  margin-bottom: 5px;\n  font-size: 30px;\n  font-weight: 400;\n  color: #333;\n}\n.half-rule {\n  width: 100px;\n  margin: 40px auto;\n}\n.bs-docs-featurette h3 {\n  margin-bottom: 5px;\n  font-weight: 400;\n  color: #333;\n}\n.bs-docs-featurette-img {\n  display: block;\n  margin-bottom: 20px;\n  color: #333;\n}\n.bs-docs-featurette-img:hover {\n  color: #337ab7;\n  text-decoration: none;\n}\n.bs-docs-featurette-img img {\n  display: block;\n  margin-bottom: 15px;\n}\n\n@media (min-width: @screen-xs-min) {\n  .bs-docs-featurette .img-responsive {\n    margin-top: 30px;\n  }\n}\n@media (min-width: @screen-sm-min) {\n  .bs-docs-featurette {\n    padding-top: 100px;\n    padding-bottom: 100px;\n  }\n  .bs-docs-featurette-title {\n    font-size: 40px;\n  }\n  .bs-docs-featurette .lead {\n    max-width: 80%;\n    margin-right: auto;\n    margin-left: auto;\n  }\n  .bs-docs-featurette .img-responsive {\n    margin-top: 0;\n  }\n}\n", ".bs-docs-featured-sites {\n  margin-right: -1px;\n  margin-left: -1px;\n}\n.bs-docs-featured-sites .col-xs-8 {\n  padding: 1px;\n}\n.bs-docs-featured-sites .img-responsive {\n  margin-top: 0;\n}\n\n@media (min-width: 768px) {\n  .bs-docs-featured-sites .col-sm-4:first-child img {\n    border-top-left-radius: 4px;\n    border-bottom-left-radius: 4px;\n  }\n  .bs-docs-featured-sites .col-sm-4:last-child img {\n    border-top-right-radius: 4px;\n    border-bottom-right-radius: 4px;\n  }\n}\n", ".bs-examples {\n  .thumbnail {\n    margin-bottom: 10px;\n  }\n\n  h4 { margin-bottom: 5px; }\n\n  p { margin-bottom: 20px; }\n\n  @media (max-width: @screen-xs-min) {\n    margin-right: -10px;\n    margin-left: -10px;\n\n    > [class^=\"col-\"] {\n      padding-right: 10px;\n      padding-left: 10px;\n    }\n  }\n}\n", "// stylelint-disable selector-max-compound-selectors\n\n// By default it's not affixed in mobile views, so undo that\n.bs-docs-sidebar.affix {\n  position: static;\n}\n@media (min-width: @screen-sm-min) {\n  .bs-docs-sidebar {\n    padding-left: 20px;\n  }\n}\n\n.bs-docs-search {\n  margin-bottom: 20px;\n  margin-left: 20px;\n}\n\n// First level of nav\n.bs-docs-sidenav {\n  margin-top: 20px;\n  margin-bottom: 20px;\n}\n\n// All levels of nav\n.bs-docs-sidebar .nav > li > a {\n  display: block;\n  padding: 4px 20px;\n  font-size: 13px;\n  font-weight: 500;\n  color: #767676;\n}\n.bs-docs-sidebar .nav > li > a:hover,\n.bs-docs-sidebar .nav > li > a:focus {\n  padding-left: 19px;\n  color: #563d7c;\n  text-decoration: none;\n  background-color: transparent;\n  border-left: 1px solid #563d7c;\n}\n.bs-docs-sidebar .nav > .active > a,\n.bs-docs-sidebar .nav > .active:hover > a,\n.bs-docs-sidebar .nav > .active:focus > a {\n  padding-left: 18px;\n  font-weight: 700;\n  color: #563d7c;\n  background-color: transparent;\n  border-left: 2px solid #563d7c;\n}\n\n// Nav: second level (shown on .active)\n.bs-docs-sidebar .nav .nav {\n  display: none; // Hide by default, but at >768px, show it\n  padding-bottom: 10px;\n}\n.bs-docs-sidebar .nav .nav > li > a {\n  padding-top: 1px;\n  padding-bottom: 1px;\n  padding-left: 30px;\n  font-size: 12px;\n  font-weight: 400;\n}\n.bs-docs-sidebar .nav .nav > li > a:hover,\n.bs-docs-sidebar .nav .nav > li > a:focus {\n  padding-left: 29px;\n}\n.bs-docs-sidebar .nav .nav > .active > a,\n.bs-docs-sidebar .nav .nav > .active:hover > a,\n.bs-docs-sidebar .nav .nav > .active:focus > a {\n  padding-left: 28px;\n  font-weight: 500;\n}\n\n// Back to top (hidden on mobile)\n.back-to-top,\n.bs-docs-theme-toggle {\n  display: none;\n  padding: 4px 10px;\n  margin-top: 10px;\n  margin-left: 10px;\n  font-size: 12px;\n  font-weight: 500;\n  color: #999;\n}\n.back-to-top:hover,\n.bs-docs-theme-toggle:hover {\n  color: #563d7c;\n  text-decoration: none;\n}\n.bs-docs-theme-toggle {\n  margin-top: 0;\n}\n\n@media (min-width: @screen-sm-min) {\n  .back-to-top,\n  .bs-docs-theme-toggle {\n    display: block;\n  }\n}\n\n// Show and affix the side nav when space allows it\n@media (min-width: @screen-md-min) {\n  .bs-docs-sidebar .nav > .active > ul {\n    display: block;\n  }\n  // Widen the fixed sidebar\n  .bs-docs-sidebar.affix,\n  .bs-docs-sidebar.affix-bottom {\n    width: 213px;\n  }\n  .bs-docs-sidebar.affix {\n    position: fixed; // Undo the static from mobile first approach\n    top: 20px;\n  }\n  .bs-docs-sidebar.affix-bottom {\n    position: absolute; // Undo the static from mobile first approach\n  }\n  .bs-docs-sidebar.affix-bottom .bs-docs-sidenav,\n  .bs-docs-sidebar.affix .bs-docs-sidenav {\n    margin-top: 0;\n    margin-bottom: 0;\n  }\n}\n@media (min-width: @screen-lg-min) {\n  // Widen the fixed sidebar again\n  .bs-docs-sidebar.affix-bottom,\n  .bs-docs-sidebar.affix {\n    width: 263px;\n  }\n}\n", "// stylelint-disable selector-max-id, selector-no-qualifying-type\n\n// Grid examples\n//\n// Highlight the grid columns within the docs so folks can see their padding,\n// alignment, sizing, etc.\n\n.show-grid {\n  margin-bottom: 15px;\n}\n.show-grid [class^=\"col-\"] {\n  padding-top: 10px;\n  padding-bottom: 10px;\n  background-color: #eee;\n  background-color: rgba(86, 61, 124, .15);\n  border: 1px solid #ddd;\n  border: 1px solid rgba(86, 61, 124, .2);\n}\n\n// Examples\n//\n// Isolated sections of example content for each component or feature. Usually\n// followed by a code snippet.\n\n.bs-example {\n  position: relative;\n  padding: 45px 15px 15px;\n  margin: 0 -15px 15px;\n  border-color: #e5e5e5 #eee #eee;\n  border-style: solid;\n  border-width: 1px 0;\n  box-shadow: inset 0 3px 6px rgba(0, 0, 0, .05);\n}\n// Echo out a label for the example\n.bs-example:after {\n  position: absolute;\n  top: 15px;\n  left: 15px;\n  font-size: 12px;\n  font-weight: 700;\n  color: #959595;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n  content: \"Example\";\n}\n\n.bs-example-padded-bottom {\n  padding-bottom: 24px;\n}\n\n// Tweak display of the code snippets when following an example\n.bs-example + .highlight,\n.bs-example + .bs-clipboard + .highlight {\n  margin: -15px -15px 15px;\n  border-width: 0 0 1px;\n  border-radius: 0;\n}\n\n// Make the examples and snippets not full-width\n@media (min-width: @screen-sm-min) {\n  .bs-example {\n    margin-right: 0;\n    margin-left: 0;\n    background-color: #fff;\n    border-color: #ddd;\n    border-width: 1px;\n    border-radius: 4px 4px 0 0;\n    box-shadow: none;\n  }\n  .bs-example + .highlight,\n  .bs-example + .bs-clipboard + .highlight {\n    margin-top: -16px;\n    margin-right: 0;\n    margin-left: 0;\n    border-width: 1px;\n    border-bottom-right-radius: 4px;\n    border-bottom-left-radius: 4px;\n  }\n  .bs-example + .bs-clipboard .btn-clipboard {\n    top: -15px; // due to padding .bs-example has\n    border-top-right-radius: 0;\n  }\n  .bs-example-standalone {\n    border-radius: 4px;\n  }\n}\n\n// Undo width of container\n.bs-example .container {\n  width: auto;\n}\n\n// Tweak content of examples for optimum awesome\n.bs-example > p:last-child,\n.bs-example > ul:last-child,\n.bs-example > ol:last-child,\n.bs-example > blockquote:last-child,\n.bs-example > .form-control:last-child,\n.bs-example > .table:last-child,\n.bs-example > .navbar:last-child,\n.bs-example > .jumbotron:last-child,\n.bs-example > .alert:last-child,\n.bs-example > .panel:last-child,\n.bs-example > .list-group:last-child,\n.bs-example > .well:last-child,\n.bs-example > .progress:last-child,\n.bs-example > .table-responsive:last-child > .table {\n  margin-bottom: 0;\n}\n.bs-example > p > .close {\n  float: none;\n}\n\n// Typography\n.bs-example-type .table .type-info {\n  color: #767676;\n  vertical-align: middle;\n}\n.bs-example-type .table td {\n  padding: 15px 0;\n  border-color: #eee;\n}\n.bs-example-type .table tr:first-child td {\n  border-top: 0;\n}\n.bs-example-type h1,\n.bs-example-type h2,\n.bs-example-type h3,\n.bs-example-type h4,\n.bs-example-type h5,\n.bs-example-type h6 {\n  margin: 0;\n}\n\n// Contextual background colors\n.bs-example-bg-classes p {\n  padding: 15px;\n}\n\n// Images\n.bs-example > .img-circle,\n.bs-example > .img-rounded,\n.bs-example > .img-thumbnail {\n  margin: 5px;\n}\n\n// Tables\n.bs-example > .table-responsive > .table {\n  background-color: #fff;\n}\n\n// Buttons\n.bs-example > .btn,\n.bs-example > .btn-group {\n  margin-top: 5px;\n  margin-bottom: 5px;\n}\n.bs-example > .btn-toolbar + .btn-toolbar {\n  margin-top: 10px;\n}\n\n// Forms\n.bs-example-control-sizing {\n  select,\n  input[type=\"text\"] + input[type=\"text\"] {\n    margin-top: 10px;\n  }\n}\n.bs-example-form .input-group {\n  margin-bottom: 10px;\n}\n.bs-example > textarea.form-control {\n  resize: vertical;\n}\n\n// List groups\n.bs-example > .list-group {\n  max-width: 400px;\n}\n\n// Navbars\n.bs-example .navbar:last-child {\n  margin-bottom: 0;\n}\n.bs-navbar-top-example,\n.bs-navbar-bottom-example {\n  z-index: 1;\n  padding: 0;\n  overflow: hidden; // cut the drop shadows off\n}\n.bs-navbar-top-example .navbar-header,\n.bs-navbar-bottom-example .navbar-header {\n  margin-left: 0;\n}\n.bs-navbar-top-example .navbar-fixed-top,\n.bs-navbar-bottom-example .navbar-fixed-bottom {\n  position: relative;\n  margin-right: 0;\n  margin-left: 0;\n}\n.bs-navbar-top-example {\n  padding-bottom: 45px;\n}\n.bs-navbar-top-example:after {\n  top: auto;\n  bottom: 15px;\n}\n.bs-navbar-top-example .navbar-fixed-top {\n  top: -1px;\n}\n.bs-navbar-bottom-example {\n  padding-top: 45px;\n}\n.bs-navbar-bottom-example .navbar-fixed-bottom {\n  bottom: -1px;\n}\n.bs-navbar-bottom-example .navbar {\n  margin-bottom: 0;\n}\n@media (min-width: 768px) {\n  .bs-navbar-top-example .navbar-fixed-top,\n  .bs-navbar-bottom-example .navbar-fixed-bottom {\n    position: absolute;\n  }\n}\n\n// Pagination\n.bs-example .pagination {\n  margin-top: 10px;\n  margin-bottom: 10px;\n}\n\n// Pager\n.bs-example > .pager {\n  margin-top: 0;\n}\n\n// Example modals\n.bs-example-modal {\n  background-color: #f5f5f5;\n}\n.bs-example-modal .modal {\n  position: relative;\n  top: auto;\n  right: auto;\n  bottom: auto;\n  left: auto;\n  z-index: 1;\n  display: block;\n}\n.bs-example-modal .modal-dialog {\n  left: auto;\n  margin-right: auto;\n  margin-left: auto;\n}\n\n// Example dropdowns\n.bs-example > .dropdown > .dropdown-toggle {\n  float: left;\n}\n.bs-example > .dropdown > .dropdown-menu {\n  position: static;\n  display: block;\n  margin-bottom: 5px;\n  clear: left;\n}\n\n// Example tabbable tabs\n.bs-example-tabs .nav-tabs {\n  margin-bottom: 15px;\n}\n\n// Tooltips\n.bs-example-tooltips {\n  text-align: center;\n}\n.bs-example-tooltips > .btn {\n  margin-top: 5px;\n  margin-bottom: 5px;\n}\n.bs-example-tooltip .tooltip {\n  position: relative;\n  display: inline-block;\n  margin: 10px 20px;\n  opacity: 1;\n}\n\n// Popovers\n.bs-example-popover {\n  padding-bottom: 24px;\n  background-color: #f9f9f9;\n}\n.bs-example-popover .popover {\n  position: relative;\n  display: block;\n  float: left;\n  width: 260px;\n  margin: 20px;\n}\n\n// Scrollspy demo on fixed height div\n.scrollspy-example {\n  position: relative;\n  height: 200px;\n  margin-top: 10px;\n  overflow: auto;\n}\n\n.bs-example > .nav-pills-stacked-example {\n  max-width: 300px;\n}\n\n// Simple collapse example\n#collapseExample .well {\n  margin-bottom: 0;\n}\n\n// Pseudo :focus state for showing how it looks in the docs\n#focusedInput {\n  border-color: rgb(204, 204, 204); // Restate unfocused value to make CSSLint happy that there's a pre-CSS3 fallback\n  border-color: rgba(82, 168, 236, .8);\n  outline: 0;\n  outline: thin dotted \\9; // IE6-9\n  box-shadow: 0 0 8px rgba(82, 168, 236, .6);\n}\n", "// Callouts\n//\n// Not quite alerts, but custom and helpful notes for folks reading the docs.\n// Requires a base and modifier class.\n\n.bs-callout {\n  padding: 20px;\n  margin: 20px 0;\n  border: 1px solid #eee;\n  border-left-width: 5px;\n  border-radius: 3px;\n\n  h4 {\n    margin-top: 0;\n    margin-bottom: 5px;\n  }\n\n  p:last-child {\n    margin-bottom: 0;\n  }\n\n  code {\n    border-radius: 3px;\n  }\n\n  + .bs-callout {\n    margin-top: -5px;\n  }\n}\n\n.bs-callout-danger {\n  border-left-color: #ce4844;\n\n  h4 {\n    color: #ce4844;\n  }\n}\n\n.bs-callout-warning {\n  border-left-color: #aa6708;\n\n  h4 {\n    color: #aa6708;\n  }\n}\n\n.bs-callout-info {\n  border-left-color: #1b809e;\n\n  h4 {\n    color: #1b809e;\n  }\n}\n", ".color-swatches {\n  margin: 0 -5px;\n  overflow: hidden; /* clearfix */\n}\n.color-swatch {\n  float: left;\n  width: 60px;\n  height: 60px;\n  margin: 0 5px;\n  border-radius: 3px;\n}\n\n@media (min-width: 768px) {\n  .color-swatch {\n    width: 100px;\n    height: 100px;\n  }\n}\n\n// Framework colors\n.color-swatches .gray-darker {\n  background-color: #222;\n}\n.color-swatches .gray-dark {\n  background-color: #333;\n}\n.color-swatches .gray {\n  background-color: #555;\n}\n.color-swatches .gray-light {\n  background-color: #999;\n}\n.color-swatches .gray-lighter {\n  background-color: #eee;\n}\n.color-swatches .brand-primary {\n  background-color: #337ab7;\n}\n.color-swatches .brand-success {\n  background-color: #5cb85c;\n}\n.color-swatches .brand-warning {\n  background-color: #f0ad4e;\n}\n.color-swatches .brand-danger {\n  background-color: #d9534f;\n}\n.color-swatches .brand-info {\n  background-color: #5bc0de;\n}\n\n// Docs colors\n.color-swatches .bs-purple {\n  background-color: #563d7c;\n}\n.color-swatches .bs-purple-light {\n  background-color: #c7bfd3;\n}\n.color-swatches .bs-purple-lighter {\n  background-color: #e5e1ea;\n}\n.color-swatches .bs-gray {\n  background-color: #f9f9f9;\n}\n", ".bs-team .team-member {\n  line-height: 32px;\n  color: #555;\n}\n.bs-team .team-member:hover {\n  color: #333;\n  text-decoration: none;\n}\n.bs-team .github-btn {\n  float: right;\n  width: 180px;\n  height: 20px;\n  margin-top: 6px;\n  border: none;\n}\n.bs-team img {\n  float: left;\n  width: 32px;\n  margin-right: 10px;\n  border-radius: 4px;\n}\n", "// stylelint-disable selector-no-qualifying-type, declaration-no-important\n\n// Responsive (scrollable) doc tables\n.table-responsive .highlight pre {\n  white-space: normal;\n}\n\n// Utility classes table\n.bs-table th small,\n.responsive-utilities th small {\n  display: block;\n  font-weight: 400;\n  color: #999;\n}\n.responsive-utilities tbody th {\n  font-weight: 400;\n}\n.responsive-utilities td {\n  text-align: center;\n}\n.responsive-utilities td.is-visible {\n  color: #468847;\n  background-color: #dff0d8 !important;\n}\n.responsive-utilities td.is-hidden {\n  color: #ccc;\n  background-color: #f9f9f9 !important;\n}\n\n// Responsive tests\n.responsive-utilities-test {\n  margin-top: 5px;\n}\n.responsive-utilities-test .col-xs-6 {\n  margin-bottom: 10px;\n}\n.responsive-utilities-test span {\n  display: block;\n  padding: 15px 10px;\n  font-size: 14px;\n  font-weight: 700;\n  line-height: 1.1;\n  text-align: center;\n  border-radius: 4px;\n}\n.visible-on .col-xs-6 .hidden-xs,\n.visible-on .col-xs-6 .hidden-sm,\n.visible-on .col-xs-6 .hidden-md,\n.visible-on .col-xs-6 .hidden-lg,\n.hidden-on .col-xs-6 .hidden-xs,\n.hidden-on .col-xs-6 .hidden-sm,\n.hidden-on .col-xs-6 .hidden-md,\n.hidden-on .col-xs-6 .hidden-lg {\n  color: #999;\n  border: 1px solid #ddd;\n}\n.visible-on .col-xs-6 .visible-xs-block,\n.visible-on .col-xs-6 .visible-sm-block,\n.visible-on .col-xs-6 .visible-md-block,\n.visible-on .col-xs-6 .visible-lg-block,\n.hidden-on .col-xs-6 .visible-xs-block,\n.hidden-on .col-xs-6 .visible-sm-block,\n.hidden-on .col-xs-6 .visible-md-block,\n.hidden-on .col-xs-6 .visible-lg-block {\n  color: #468847;\n  background-color: #dff0d8;\n  border: 1px solid #d6e9c6;\n}\n", ".bs-glyphicons {\n  margin: 0 -10px 20px;\n  overflow: hidden;\n}\n.bs-glyphicons-list {\n  padding-left: 0;\n  list-style: none;\n}\n.bs-glyphicons li {\n  float: left;\n  width: 25%;\n  height: 115px;\n  padding: 10px;\n  font-size: 10px;\n  line-height: 1.4;\n  text-align: center;\n  background-color: #f9f9f9;\n  border: 1px solid #fff;\n}\n.bs-glyphicons .glyphicon {\n  margin-top: 5px;\n  margin-bottom: 10px;\n  font-size: 24px;\n}\n.bs-glyphicons .glyphicon-class {\n  display: block;\n  text-align: center;\n  word-wrap: break-word; // Help out IE10+ with class names\n}\n.bs-glyphicons li:hover {\n  color: #fff;\n  background-color: #563d7c;\n}\n\n@media (min-width: 768px) {\n  .bs-glyphicons {\n    margin-right: 0;\n    margin-left: 0;\n  }\n  .bs-glyphicons li {\n    width: 12.5%;\n    font-size: 12px;\n  }\n}\n", "// stylelint-disable selector-max-id, selector-no-qualifying-type\n\n.bs-customizer .toggle {\n  float: right;\n  margin-top: 25px;\n}\n\n// Headings and form contrls\n.bs-customizer label {\n  margin-top: 10px;\n  font-weight: 500;\n  color: #555;\n}\n.bs-customizer h2 {\n  padding-top: 30px;\n  margin-top: 0;\n  margin-bottom: 5px;\n}\n.bs-customizer h3 {\n  margin-bottom: 0;\n}\n.bs-customizer h4 {\n  margin-top: 15px;\n  margin-bottom: 0;\n}\n.bs-customizer .bs-callout h4 {\n  margin-top: 0; // lame, but due to specificity we have to duplicate\n  margin-bottom: 5px;\n}\n.bs-customizer input[type=\"text\"] {\n  font-family: Menlo, Monaco, Consolas, \"Courier New\", monospace;\n  background-color: #fafafa;\n}\n.bs-customizer .help-block {\n  margin-bottom: 5px;\n  font-size: 12px;\n}\n\n// For the variables, use regular weight\n#less-section label {\n  font-weight: 400;\n}\n\n// Downloads\n.bs-customize-download .btn-outline {\n  padding: 20px;\n}\n\n// Error handling\n.bs-customizer-alert {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 1030;\n  padding: 15px 0;\n  color: #fff;\n  background-color: #d9534f;\n  border-bottom: 1px solid #b94441;\n  box-shadow: inset 0 1px 0 rgba(255, 255, 255, .25);\n}\n.bs-customizer-alert .close {\n  margin-top: -4px;\n  font-size: 24px;\n}\n.bs-customizer-alert p {\n  margin-bottom: 0;\n}\n.bs-customizer-alert .glyphicon {\n  margin-right: 5px;\n}\n.bs-customizer-alert pre {\n  margin: 10px 0 0;\n  color: #fff;\n  background-color: #a83c3a;\n  border-color: #973634;\n  box-shadow: inset 0 2px 4px rgba(0, 0, 0, .05), 0 1px 0 rgba(255, 255, 255, .1);\n}\n\n.bs-dropzone {\n  position: relative;\n  padding: 20px;\n  margin-bottom: 20px;\n  color: #777;\n  text-align: center;\n  border: 2px dashed #eee;\n  border-radius: 4px;\n}\n.bs-dropzone .import-header {\n  margin-bottom: 5px;\n}\n.bs-dropzone .glyphicon-folder-open {\n  font-size: 40px;\n}\n.bs-dropzone hr {\n  width: 100px;\n}\n.bs-dropzone .lead {\n  margin-bottom: 10px;\n  font-weight: 400;\n  color: #333;\n}\n#import-manual-trigger {\n  cursor: pointer;\n}\n.bs-dropzone p:last-child {\n  margin-bottom: 0;\n}\n", "// Logo series wrapper\n.bs-brand-logos {\n  display: table;\n  width: 100%;\n  margin-bottom: 15px;\n  overflow: hidden;\n  color: #563d7c;\n  background-color: #f9f9f9;\n  border-radius: 4px;\n}\n\n// Individual items\n.bs-brand-item {\n  padding: 60px 0;\n  text-align: center;\n}\n.bs-brand-item + .bs-brand-item {\n  border-top: 1px solid #fff;\n}\n.bs-brand-logos .inverse {\n  color: #fff;\n  background-color: #563d7c;\n}\n\n// Heading content within\n.bs-brand-item h1,\n.bs-brand-item h3 {\n  margin-top: 0;\n  margin-bottom: 0;\n}\n.bs-brand-item .bs-docs-booticon {\n  margin-right: auto;\n  margin-left: auto;\n}\n\n// Make the icons stand out on what is/isn't okay\n.bs-brand-item .glyphicon {\n  width: 30px;\n  height: 30px;\n  margin: 10px auto -10px;\n  line-height: 30px;\n  color: #fff;\n  border-radius: 50%;\n}\n.bs-brand-item .glyphicon-ok {\n  background-color: #5cb85c;\n}\n.bs-brand-item .glyphicon-remove {\n  background-color: #d9534f;\n}\n\n@media (min-width: @screen-sm-min) {\n  .bs-brand-item {\n    display: table-cell;\n    width: 1%;\n  }\n  .bs-brand-item + .bs-brand-item {\n    border-top: 0;\n    border-left: 1px solid #fff;\n  }\n  .bs-brand-item h1 {\n    font-size: 60px;\n  }\n}\n", "// clipboard.js\n//\n// JS-based `Copy` buttons for code snippets.\n\n.bs-clipboard {\n  position: relative;\n  display: none;\n  float: right;\n\n  + .highlight {\n    margin-top: 0;\n  }\n}\n\n.btn-clipboard {\n  position: absolute;\n  top: 0;\n  right: 0;\n  z-index: 10;\n  display: block;\n  padding: 4px 8px;\n  font-size: 12px;\n  color: #818a91;\n  cursor: pointer;\n  background-color: transparent;\n  border: 0;\n  border-top-right-radius: 4px;\n  border-bottom-left-radius: 4px;\n\n  &:hover {\n    color: #fff;\n    background-color: #027de7;\n  }\n}\n\n@media (min-width: @screen-sm-min) {\n  .bs-clipboard {\n    display: block;\n  }\n}\n", ".anchorjs-link {\n  color: inherit;\n}\n\n@media (max-width: 480px) {\n  .anchorjs-link {\n    display: none;\n  }\n}\n\n*:hover > .anchorjs-link {\n  opacity: .75;\n  transition: color .16s linear;\n}\n\n*:hover > .anchorjs-link:hover,\n.anchorjs-link:focus {\n  text-decoration: none;\n  opacity: 1;\n}\n", "// stylelint-disable declaration-no-important\n\n// Docsearch overrides\n//\n// `!important` indicates overridden properties.\n.algolia-autocomplete {\n  display: block !important;\n\n  // Menu container\n  .ds-dropdown-menu {\n    width: 100%;\n    min-width: 0 !important;\n    max-width: none !important;\n    padding: 10px 0 !important;\n    background-color: #fff;\n    background-clip: padding-box;\n    border: 1px solid #ddd;\n    border: 1px solid rgba(0, 0, 0, .1);\n    box-shadow: 0 8px 15px rgba(0, 0, 0, .175);\n\n    @media (min-width: @screen-sm-min) {\n      width: 175%;\n    }\n\n    // Caret\n    &:before {\n      display: none !important;\n    }\n\n    [class^=\"ds-dataset-\"] {\n      padding: 0 !important;\n      overflow: visible !important;\n      background-color: transparent !important;\n      border: 0 !important;\n    }\n\n    .ds-suggestions {\n      margin-top: 0 !important;\n    }\n\n    .ds-input {\n      box-shadow: none;\n    }\n  }\n\n  .algolia-docsearch-suggestion {\n    padding: 0 !important;\n    overflow: visible !important;\n  }\n\n  .algolia-docsearch-suggestion--category-header {\n    padding: 2px 15px !important;\n    margin-top: 0 !important;\n    font-size: 13px !important;\n    font-weight: 500 !important;\n    color: #7952b3 !important;\n    border-bottom: 0 !important;\n  }\n\n  .algolia-docsearch-suggestion--wrapper {\n    float: none !important;\n    padding-top: 0 !important;\n  }\n\n  // Section header\n  .algolia-docsearch-suggestion--subcategory-column {\n    float: none !important;\n    width: auto !important;\n    padding: 0 !important;\n    text-align: left !important;\n  }\n\n  .algolia-docsearch-suggestion--content {\n    float: none !important;\n    width: auto !important;\n    padding: 0 !important;\n\n    // Vertical divider between column header and content\n    &:before {\n      display: none !important;\n    }\n  }\n\n  .ds-suggestion {\n    &:not(:first-child) {\n      .algolia-docsearch-suggestion--category-header {\n        padding-top: 10px !important;\n        margin-top: 10px !important;\n        border-top: 1px solid #eee;\n      }\n    }\n\n    .algolia-docsearch-suggestion--subcategory-column {\n      display: none !important;\n    }\n  }\n\n  .algolia-docsearch-suggestion--title {\n    display: block;\n    padding: 4px 15px !important;\n    margin-bottom: 0 !important;\n    font-size: 13px !important;\n    font-weight: 400 !important;\n  }\n\n  .algolia-docsearch-suggestion--text {\n    padding: 0 15px 8px !important;\n    margin-top: -4px;\n    font-size: 13px !important;\n    font-weight: 400;\n    line-height: 1.25 !important;\n  }\n\n  .algolia-docsearch-footer {\n    float: none !important;\n    width: auto !important;\n    height: auto !important;\n    padding: 10px 15px 0;\n    font-size: 10px !important;\n    line-height: 1 !important;\n    color: #767676 !important;\n    border-top: 1px solid #eee;\n  }\n\n  .algolia-docsearch-footer--logo {\n    display: inline !important;\n    overflow: visible !important;\n    color: inherit !important;\n    text-indent: 0 !important;\n    background: none !important;\n  }\n\n  .algolia-docsearch-suggestion--highlight {\n    color: #5f2dab;\n    background-color: #eee;\n  }\n\n  .algolia-docsearch-suggestion--text .algolia-docsearch-suggestion--highlight {\n    box-shadow: inset 0 -2px 0 0 rgba(95, 45, 171, .5) !important;\n  }\n\n  .ds-suggestion.ds-cursor .algolia-docsearch-suggestion--content {\n    background-color: #e5e5e5 !important;\n  }\n}\n", "// stylelint-disable selector-max-type, selector-no-qualifying-type, declaration-no-important\n\n//\n// Misc\n//\n\n// For scrollspy\nbody {\n  position: relative;\n}\n\n// Keep code small in tables on account of limited space\n.table code {\n  font-size: 13px;\n  font-weight: 400;\n}\n\n// Inline code within headings retain the heading's background-color\nh2 code,\nh3 code,\nh4 code {\n  background-color: inherit;\n}\n\n// Space docs sections out\n.bs-docs-section {\n  margin-bottom: 60px;\n}\n.bs-docs-section:last-child {\n  margin-bottom: 0;\n}\n\nh1[id] {\n  padding-top: 20px;\n  margin-top: 0;\n}\n\n// Wall of Browser Bugs\n.bs-docs-browser-bugs td p {\n  margin-bottom: 0;\n}\n\n.bs-docs-browser-bugs th:first-child {\n  width: 18%;\n}\n\n// Don't wrap event names in Events tables in JS plugin docs\n.bs-events-table > thead > tr > th:first-child,\n.bs-events-table > tbody > tr > td:first-child {\n  white-space: nowrap;\n}\n\n.bs-events-table > thead > tr > th:first-child {\n  width: 150px;\n}\n\n.js-options-table > thead > tr > th:nth-child(1),\n.js-options-table > thead > tr > th:nth-child(2) {\n  width: 100px;\n}\n\n.js-options-table > thead > tr > th:nth-child(3) {\n  width: 50px;\n}\n\n// v4 notice above main navbar\n.v4-tease {\n  display: block;\n  padding: 15px 20px;\n  font-weight: 700;\n  color: #fff;\n  text-align: center;\n  background-color: #0275d8;\n\n  &:focus,\n  &:hover {\n    color: #fff;\n    text-decoration: none;\n    background-color: #0269c2;\n  }\n}\n\n/* Nullify ill-advised printing of hrefs; see #18711 */\n@media print {\n  a[href]:after {\n    content: \"\" !important;\n  }\n}\n"]}