{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, user-scalable=yes, initial-scale=0.3, maxmum-scale=1.0, minimum-scale=0.3">
    <title> 登录</title>
    <style>
        @charset "utf-8";

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
        font-family: 'Arial', sans-serif;
        background-image: url("{% static 'img/4.jpg' %}");
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        margin: 0;
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
    }

        /* 头部样式 */
        .header {
            height: 90px;
            background: linear-gradient(135deg, #9b1ef5 0%, #ac1ce6 100%);
            padding: 10px 5%;
            display: flex;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .Logo {
            width: 72px;
            height: 72px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid white;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        .LogoName {
            font-size: 28px;
            color: white;
            margin-left: 20px;
            font-weight: 500;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
        }

        /* 主要内容区 */
        .wrap {
            min-height: calc(100vh - 90px);
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 40px 20px;
        }

        .logWrap {
            display: flex;
            max-width: 1000px;
            width: 100%;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .banner {
            width: 50%;
            object-fit: cover;
            display: block;
        }

        .logShow {
            width: 50%;
            padding: 40px;
            display: flex;
            flex-direction: column;
        }

        /* 登录表单样式 */
        .LoginTop {
            text-align: center;
            margin-bottom: 40px;
        }

        .LoginTop .p1 {
            font-size: 28px;
            color: #1976d2;
            font-weight: 600;
            position: relative;
            padding-bottom: 15px;
        }

        .LoginTop .p1::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #21a4ee, #1976d2);
            border-radius: 3px;
        }

        .InputStr {
            position: relative;
            margin-bottom: 25px;
        }

        .InputStr img {
            position: absolute;
            top: 15px;
            left: 15px;
            z-index: 1;
            opacity: 0.6;
        }

        .InputStr input {
            width: 100%;
            height: 50px;
            padding: 0 20px 0 50px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: all 0.3s;
        }

        .InputStr input:focus {
            border-color: #21a4ee;
            box-shadow: 0 0 0 3px rgba(33, 164, 238, 0.2);
            outline: none;
        }

        .InputStr span {
            display: block;
            margin-top: 5px;
            font-size: 14px;
        }

        /* 按钮样式 */
        .LoginButton {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .LoginButton button {
            flex: 1;
            height: 50px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
        }

        .LoginButton button[type="submit"] {
            background: linear-gradient(135deg, #21a4ee 0%, #1976d2 100%);
            color: white;
        }

        .LoginButton button[type="submit"]:hover {
            background: linear-gradient(135deg, #1e9ae0 0%, #1565c0 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(33, 164, 238, 0.3);
        }

        .LoginButton button[type="reset"] {
            background: #f5f7fa;
            color: #666;
            border: 1px solid #ddd;
        }

        .LoginButton button[type="reset"]:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }

        /* 底部链接 */
        .form-footer {
            text-align: center;
            margin-top: 30px;
            font-size: 14px;
        }

        .form-footer a {
            color: #21a4ee;
            text-decoration: none;
            transition: color 0.3s;
        }

        .form-footer a:hover {
            color: #1976d2;
            text-decoration: underline;
        }

        .error-message {
            color: #e53935;
            text-align: center;
            margin-top: 15px;
            font-size: 14px;
            min-height: 20px;
        }

        
        .radio-group {
    display: flex;
    gap: 15px;
    margin-top: 8px;
}

.radio-label {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
}

.form-group {
    margin-bottom: 15px;
}
        
    </style>
</head>
<body>


    <!--中部内容-->
    <div class="wrap">
        <div class="logWrap">
            <img class="banner" src="/static/img/6.jpg" alt="登录背景图">
            <div class="logShow">
                <!-- 头部提示信息 -->
                <div class="LoginIndex LoginTop">
                    <p class="p1">登录</p>
                </div>
                
                <form method="post" action="/alogin/" novalidate>
                    <!-- 用户名输入框 -->
                    <div class="InputStr">
                        <img src="/static/user.png" width="20" height="20" alt="用户名图标"/>
                     <input type="text" name="username" placeholder="用户名" class="form-control" required>
                     
                    </div>
                    
                    <!-- 密码输入框 -->
                    <div class="InputStr">
                        <img src="/static/password.png" width="20" height="20" alt="密码图标"/>
                       <input type="password" name="password" placeholder="密码" class="form-control" required>

                    </div>
                    


                    <!-- 登录按钮 -->
                    <div class="LoginButton">
                        <button type="submit">登 录</button>
                        <button type="reset">重 置</button>
                    </div>
                    


                    <div class="error-message">
                        {{ error }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>
</html>