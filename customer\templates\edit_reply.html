{% extends 'base.html' %}
{% block content %}
{% load static %}

<style>


.container1  {
    max-width: 1200px;
    margin: 30px auto;
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    margin-top: 80px;
}

.container1 h1 {
    color: #2c3e50;
    margin-bottom: 25px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    font-size: 28px;
}

/* 表单样式 */
.container1 form {
    margin-bottom: 30px;
}

.container1 form p {
    margin-bottom: 20px;
}

.container1 label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
}

input[type="text"],
textarea,
select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.container1 textarea {
    min-height: 150px;
    resize: vertical;
}

input[type="text"]:focus,
textarea:focus,
select:focus {
    border-color: #3498db;
    outline: none;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

/* 按钮样式 */
button[type="submit"] {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: background-color 0.3s;
    display: inline-block;
}

button[type="submit"]:hover {
    background-color: #2980b9;
}

/* 返回链接样式 */
.container1 a {
    color: #3498db;
    text-decoration: none;
    display: inline-block;
    margin-top: 15px;
    padding: 8px 15px;
    border: 1px solid #3498db;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.container1 a:hover {
    background-color: #f8f9fa;
    text-decoration: none;
}

/* 错误信息样式 */
.errorlist {
    color: #e74c3c;
    margin-top: 5px;
    padding-left: 0;
    list-style: none;
}

.errorlist li {
    margin-bottom: 5px;
}

/* 响应式设计 */
@media (max-width: 600px) {
    .container {
        padding: 20px;
    }
    
    h1 {
        font-size: 24px;
    }
    
    input[type="text"],
    textarea,
    select {
        padding: 10px 12px;
    }
}
</style>

<div class="container1">
    <h1>编辑回复</h1>

    <form method="POST">
        {% csrf_token %}
        {{ form.as_p }}
        <button type="submit">保存回复</button>
    </form>

    <br>
    <a href="{% url 'afeed_back' %}">返回反馈列表</a>
</div>

{% endblock %}