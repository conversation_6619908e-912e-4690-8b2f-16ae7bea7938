<div class="bs-docs-section">
  <h1 id="code" class="page-header">Code</h1>

  <h2 id="code-inline">Inline</h2>
  <p>Wrap inline snippets of code with <code>&lt;code&gt;</code>.</p>
<div class="bs-example" data-example-id="inline-code">
  For example, <code>&lt;section&gt;</code> should be wrapped as inline.
</div>
{% highlight html %}
For example, <code>&lt;section&gt;</code> should be wrapped as inline.
{% endhighlight %}

  <h2 id="code-user-input">User input</h2>
  <p>Use the <code>&lt;kbd&gt;</code> to indicate input that is typically entered via keyboard.</p>
<div class="bs-example" data-example-id="simple-kbd">
  To switch directories, type <kbd>cd</kbd> followed by the name of the directory.<br>
  To edit settings, press <kbd><kbd>ctrl</kbd> + <kbd>,</kbd></kbd>
</div>
{% highlight html %}
To switch directories, type <kbd>cd</kbd> followed by the name of the directory.<br>
To edit settings, press <kbd><kbd>ctrl</kbd> + <kbd>,</kbd></kbd>
{% endhighlight %}

  <h2 id="code-block">Basic block</h2>
  <p>Use <code>&lt;pre&gt;</code> for multiple lines of code. Be sure to escape any angle brackets in the code for proper rendering.</p>
<div class="bs-example" data-example-id="simple-pre">
  <pre>&lt;p&gt;Sample text here...&lt;/p&gt;</pre>
</div>
{% highlight html %}
<pre>&lt;p&gt;Sample text here...&lt;/p&gt;</pre>
{% endhighlight %}

  <p>You may optionally add the <code>.pre-scrollable</code> class, which will set a max-height of 350px and provide a y-axis scrollbar.</p>
  <h2 id="code-variables">Variables</h2>
  <p>For indicating variables use the <code>&lt;var&gt;</code> tag.</p>
  <div class="bs-example" data-example-id="simple-var">
    <p><var>y</var> = <var>m</var><var>x</var> + <var>b</var></p>

  </div>
{% highlight html %}
<var>y</var> = <var>m</var><var>x</var> + <var>b</var>
{% endhighlight %}

  <h2 id="code-sample-output">Sample output</h2>
  <p>For indicating blocks sample output from a program use the <code>&lt;samp&gt;</code> tag.</p>
  <div class="bs-example" data-example-id="simple-samp">
    <p><samp>This text is meant to be treated as sample output from a computer program.</samp></p>
  </div>
{% highlight html %}
<samp>This text is meant to be treated as sample output from a computer program.</samp>
{% endhighlight %}
</div>
